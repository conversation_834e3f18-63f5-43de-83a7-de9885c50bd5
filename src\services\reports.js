import { db } from './supabase';
import * as XLSX from 'xlsx';
import toast from 'react-hot-toast';
import apiService from './api';

class ReportsService {
  // Generate comprehensive reports
  async generateReport(filters = {}) {
    try {
      // Get all students with their certificates
      const students = await db.users.getAll({ role: 'student' });
      const certificates = await db.certificates.getAll();
      const categories = await db.categories.getAll();

      // Process data with comprehensive evaluation
      const reportData = students.map(student => {
        const studentCerts = certificates.filter(cert => cert.user_id === student.id);

        // Mock course grades for demonstration (in real implementation, fetch from database)
        const mockCourseGrades = [
          { course_name: '数据结构', credits: 4, grade: 85 },
          { course_name: '算法设计', credits: 3, grade: 92 },
          { course_name: '数据库原理', credits: 3, grade: 78 }
        ];

        // Calculate comprehensive score using new method
        const comprehensiveScore = apiService.calculateComprehensiveScore(mockCourseGrades, studentCerts);
        const scoreBreakdown = apiService.calculateScoreBreakdown(studentCerts);

        return {
          ...student,
          certificates: studentCerts,
          course_grades: mockCourseGrades,
          score_breakdown: scoreBreakdown,
          comprehensive_score: comprehensiveScore,
          total_score: comprehensiveScore.comprehensiveScore,
          approved_certificates: studentCerts.filter(cert => cert.status === 'approved').length,
          pending_certificates: studentCerts.filter(cert => cert.status === 'pending').length,
          rejected_certificates: studentCerts.filter(cert => cert.status === 'rejected').length
        };
      });

      // Apply filters
      let filteredData = reportData;
      
      if (filters.class_name) {
        filteredData = filteredData.filter(student => 
          student.class_name === filters.class_name
        );
      }

      if (filters.min_score) {
        filteredData = filteredData.filter(student => 
          student.total_score >= filters.min_score
        );
      }

      if (filters.date_range) {
        const { start, end } = filters.date_range;
        filteredData = filteredData.filter(student => {
          const studentCerts = student.certificates.filter(cert => {
            const certDate = new Date(cert.created_at);
            return certDate >= new Date(start) && certDate <= new Date(end);
          });
          return studentCerts.length > 0;
        });
      }

      return {
        students: filteredData,
        categories,
        summary: this.generateSummary(filteredData, categories)
      };
    } catch (error) {
      console.error('Error generating report:', error);
      throw error;
    }
  }

  // Generate summary statistics
  generateSummary(students, categories) {
    const summary = {
      total_students: students.length,
      total_certificates: 0,
      approved_certificates: 0,
      pending_certificates: 0,
      rejected_certificates: 0,
      average_score: 0,
      top_students: [],
      category_stats: {},
      class_stats: {}
    };

    // Calculate totals
    students.forEach(student => {
      summary.total_certificates += student.certificates.length;
      summary.approved_certificates += student.approved_certificates;
      summary.pending_certificates += student.pending_certificates;
      summary.rejected_certificates += student.rejected_certificates;
    });

    // Calculate average score
    if (students.length > 0) {
      const totalScore = students.reduce((sum, student) => sum + student.total_score, 0);
      summary.average_score = totalScore / students.length;
    }

    // Get top 10 students
    summary.top_students = students
      .sort((a, b) => b.total_score - a.total_score)
      .slice(0, 10)
      .map(student => ({
        name: student.name,
        student_id: student.student_id,
        class_name: student.class_name,
        total_score: student.total_score,
        approved_certificates: student.approved_certificates
      }));

    // Category statistics
    categories.forEach(category => {
      const categoryId = category.id;
      const categoryCerts = students.flatMap(student => 
        student.certificates.filter(cert => cert.category_id === categoryId)
      );
      
      summary.category_stats[category.category_name] = {
        total: categoryCerts.length,
        approved: categoryCerts.filter(cert => cert.status === 'approved').length,
        pending: categoryCerts.filter(cert => cert.status === 'pending').length,
        rejected: categoryCerts.filter(cert => cert.status === 'rejected').length,
        average_score: categoryCerts.length > 0 
          ? categoryCerts.reduce((sum, cert) => sum + (cert.score || 0), 0) / categoryCerts.length 
          : 0
      };
    });

    // Class statistics
    const classes = [...new Set(students.map(student => student.class_name))];
    classes.forEach(className => {
      const classStudents = students.filter(student => student.class_name === className);
      const classCerts = classStudents.flatMap(student => student.certificates);
      
      summary.class_stats[className] = {
        student_count: classStudents.length,
        total_certificates: classCerts.length,
        approved_certificates: classCerts.filter(cert => cert.status === 'approved').length,
        average_score: classStudents.length > 0 
          ? classStudents.reduce((sum, student) => sum + student.total_score, 0) / classStudents.length 
          : 0,
        top_student: classStudents.sort((a, b) => b.total_score - a.total_score)[0]
      };
    });

    return summary;
  }

  // Export to Excel
  async exportToExcel(reportData, filename = 'student_report') {
    try {
      const workbook = XLSX.utils.book_new();

      // Students sheet
      const studentsData = reportData.students.map(student => ({
        '学号': student.student_id,
        '姓名': student.name,
        '班级': student.class_name,
        '总分': student.total_score,
        '专业分': student.score_breakdown.professional,
        '体育分': student.score_breakdown.sports,
        '德育分': student.score_breakdown.moral,
        '基础分': student.score_breakdown.base,
        '已通过证书': student.approved_certificates,
        '待审核证书': student.pending_certificates,
        '未通过证书': student.rejected_certificates,
        '总证书数': student.certificates.length
      }));

      const studentsSheet = XLSX.utils.json_to_sheet(studentsData);
      XLSX.utils.book_append_sheet(workbook, studentsSheet, '学生成绩');

      // Summary sheet
      const summaryData = [
        ['统计项目', '数值'],
        ['学生总数', reportData.summary.total_students],
        ['证书总数', reportData.summary.total_certificates],
        ['已通过证书', reportData.summary.approved_certificates],
        ['待审核证书', reportData.summary.pending_certificates],
        ['未通过证书', reportData.summary.rejected_certificates],
        ['平均分', reportData.summary.average_score.toFixed(2)],
        ['', ''],
        ['优秀学生 (前10名)', ''],
        ['排名', '学号', '姓名', '班级', '总分', '证书数']
      ];

      reportData.summary.top_students.forEach((student, index) => {
        summaryData.push([
          index + 1,
          student.student_id,
          student.name,
          student.class_name,
          student.total_score,
          student.approved_certificates
        ]);
      });

      const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, '统计汇总');

      // Category stats sheet
      const categoryData = [
        ['证书类别', '总数', '已通过', '待审核', '未通过', '平均分']
      ];

      Object.entries(reportData.summary.category_stats).forEach(([category, stats]) => {
        categoryData.push([
          category,
          stats.total,
          stats.approved,
          stats.pending,
          stats.rejected,
          stats.average_score.toFixed(2)
        ]);
      });

      const categorySheet = XLSX.utils.aoa_to_sheet(categoryData);
      XLSX.utils.book_append_sheet(workbook, categorySheet, '类别统计');

      // Class stats sheet
      const classData = [
        ['班级', '学生数', '证书总数', '已通过证书', '平均分', '最高分学生']
      ];

      Object.entries(reportData.summary.class_stats).forEach(([className, stats]) => {
        classData.push([
          className,
          stats.student_count,
          stats.total_certificates,
          stats.approved_certificates,
          stats.average_score.toFixed(2),
          stats.top_student ? `${stats.top_student.name}(${stats.top_student.total_score})` : '-'
        ]);
      });

      const classSheet = XLSX.utils.aoa_to_sheet(classData);
      XLSX.utils.book_append_sheet(workbook, classSheet, '班级统计');

      // Generate file
      const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
      const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      // Download file
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${filename}_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success('报表导出成功！');
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('导出失败');
      throw error;
    }
  }

  // Calculate score breakdown using api service
  calculateScoreBreakdown(certificates) {
    return apiService.calculateScoreBreakdown(certificates);
  }

  // Get dashboard statistics
  async getDashboardStats(userId = null, role = 'student') {
    try {
      if (role === 'student' && userId) {
        // Student dashboard stats
        const certificates = await db.certificates.getAll({ user_id: userId });

        // Mock course grades for demonstration
        const mockCourseGrades = [
          { course_name: '数据结构', credits: 4, grade: 85 },
          { course_name: '算法设计', credits: 3, grade: 92 },
          { course_name: '数据库原理', credits: 3, grade: 78 }
        ];

        const comprehensiveScore = apiService.calculateComprehensiveScore(mockCourseGrades, certificates);
        const scoreBreakdown = this.calculateScoreBreakdown(certificates);

        return {
          totalCertificates: certificates.length,
          pendingCertificates: certificates.filter(cert => cert.status === 'pending').length,
          approvedCertificates: certificates.filter(cert => cert.status === 'approved').length,
          rejectedCertificates: certificates.filter(cert => cert.status === 'rejected').length,
          comprehensiveScore: comprehensiveScore,
          totalScore: comprehensiveScore.comprehensiveScore,
          scoreBreakdown
        };
      } else if (role === 'teacher') {
        // Teacher dashboard stats
        const students = await db.users.getAll({ role: 'student' });
        const certificates = await db.certificates.getAll();
        const categories = await db.categories.getAll();
        
        return {
          totalStudents: students.length,
          totalCertificates: certificates.length,
          pendingCertificates: certificates.filter(cert => cert.status === 'pending').length,
          approvedCertificates: certificates.filter(cert => cert.status === 'approved').length,
          rejectedCertificates: certificates.filter(cert => cert.status === 'rejected').length,
          totalCategories: categories.length,
          recentActivities: await this.getRecentActivities()
        };
      }
    } catch (error) {
      console.error('Error getting dashboard stats:', error);
      throw error;
    }
  }

  // Get recent activities for dashboard
  async getRecentActivities(limit = 10) {
    try {
      const logs = await db.logs.getAll();
      return logs.slice(0, limit);
    } catch (error) {
      console.error('Error getting recent activities:', error);
      return [];
    }
  }
}

// Create singleton instance
const reportsService = new ReportsService();

export default reportsService;
