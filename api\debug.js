import { createClient } from '@supabase/supabase-js';

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Check environment variables
    const envCheck = {
      SUPABASE_URL: !!process.env.SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      SUPABASE_JWT_SECRET: !!process.env.SUPABASE_JWT_SECRET,
      NODE_ENV: process.env.NODE_ENV,
      SUPABASE_URL_VALUE: process.env.SUPABASE_URL ? process.env.SUPABASE_URL.substring(0, 30) + '...' : 'NOT_SET'
    };

    // Test database connection
    let dbTest = { connected: false, error: null };

    if (process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      try {
        const supabase = createClient(
          process.env.SUPABASE_URL,
          process.env.SUPABASE_SERVICE_ROLE_KEY
        );

        // Test basic connection by counting users
        const { count, error } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });

        if (error) {
          dbTest.error = error.message;
        } else {
          dbTest.connected = true;
          dbTest.userCount = count;
        }

        // Try to find admin user
        const { data: adminUser, error: adminError } = await supabase
          .from('users')
          .select('id, username, student_id, role, is_super_admin')
          .or('username.eq."admin",student_id.eq."admin"')
          .single();

        if (adminError) {
          dbTest.adminError = adminError.message;
        } else {
          dbTest.adminFound = adminUser;
        }

      } catch (error) {
        dbTest.error = error.message;
      }
    } else {
      dbTest.error = 'Missing environment variables';
    }

    res.status(200).json({
      message: 'Debug info',
      timestamp: new Date().toISOString(),
      environment: envCheck,
      database: dbTest
    });

  } catch (error) {
    res.status(500).json({
      message: 'Debug error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
