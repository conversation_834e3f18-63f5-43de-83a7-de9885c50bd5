import { createClient } from '@supabase/supabase-js';

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Check environment variables
    const envCheck = {
      SUPABASE_URL: !!process.env.SUPABASE_URL,
      SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      SUPABASE_JWT_SECRET: !!process.env.SUPABASE_JWT_SECRET,
      NODE_ENV: process.env.NODE_ENV
    };

    // Test Supabase connection
    let dbTest = { connected: false, error: null, userCount: 0 };
    
    if (process.env.SUPABASE_URL && process.env.SUPABASE_SERVICE_ROLE_KEY) {
      try {
        const supabase = createClient(
          process.env.SUPABASE_URL,
          process.env.SUPABASE_SERVICE_ROLE_KEY
        );

        // Test basic connection
        const { data, error } = await supabase
          .from('users')
          .select('count')
          .limit(1);

        if (error) {
          dbTest.error = error.message;
        } else {
          dbTest.connected = true;
        }

        // Try to count users
        const { count, error: countError } = await supabase
          .from('users')
          .select('*', { count: 'exact', head: true });

        if (!countError) {
          dbTest.userCount = count;
        }

        // Try to find admin user specifically
        const { data: adminUser, error: adminError } = await supabase
          .from('users')
          .select('id, username, student_id, role')
          .or('username.eq."admin",student_id.eq."admin"')
          .single();

        if (adminError) {
          dbTest.adminError = adminError.message;
        } else {
          dbTest.adminFound = adminUser;
        }

      } catch (error) {
        dbTest.error = error.message;
      }
    }

    res.status(200).json({
      message: 'Debug info',
      timestamp: new Date().toISOString(),
      environment: envCheck,
      database: dbTest
    });

  } catch (error) {
    res.status(500).json({
      message: 'Debug error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
}
