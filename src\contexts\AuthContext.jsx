import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase, db } from '../services/supabase';
import toast from 'react-hot-toast';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in on app start
    checkAuthStatus();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          await fetchUserProfile(session.user.id);
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const checkAuthStatus = async () => {
    try {
      // Get current session from Supabase
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('Session error:', error);
        setLoading(false);
        return;
      }

      if (session) {
        // Fetch user profile from database
        await fetchUserProfile(session.user.id);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setLoading(false);
    }
  };

  const fetchUserProfile = async (userId) => {
    try {
      const userData = await db.users.getById(userId);
      setUser(userData);
    } catch (error) {
      console.error('Error fetching user profile:', error);
      // If user not found in database, create from auth user
      const { data: { user: authUser } } = await supabase.auth.getUser();
      if (authUser) {
        // Create user record in database
        const newUser = {
          id: authUser.id,
          email: authUser.email,
          name: authUser.user_metadata?.name || authUser.email,
          role: 'student', // Default role
          created_at: new Date().toISOString()
        };

        try {
          const createdUser = await db.users.create(newUser);
          setUser(createdUser);
        } catch (createError) {
          console.error('Error creating user:', createError);
          setUser(null);
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const login = async (studentIdOrCredentials, password = null) => {
    try {
      setLoading(true);

      // Handle both old and new calling patterns
      let credentials;
      if (typeof studentIdOrCredentials === 'string') {
        // Old pattern: login(studentId, password)
        credentials = {
          username: studentIdOrCredentials,
          password: password
        };
      } else {
        // New pattern: login(credentials)
        credentials = studentIdOrCredentials;
      }

      // Validate credentials
      if (!credentials || !credentials.username || !credentials.password) {
        return { success: false, message: '请输入账号和密码' };
      }

      // First try to find user by student_id or username
      let userData = null;

      try {
        if (credentials.username.match(/^\d+$/)) {
          // If username is numeric, treat as student_id
          userData = await db.users.getByStudentId(credentials.username);
        } else {
          // Otherwise, find by username only (no email field in users table)
          const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('username', credentials.username)
            .single();

          if (error) throw error;
          userData = data;
        }
      } catch (error) {
        console.error('User lookup error:', error);
        return { success: false, message: '用户不存在' };
      }

      // Use our backend API for authentication
      if (!userData) {
        return { success: false, message: '用户不存在' };
      }

      // Call our login API endpoint
      try {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            student_id: credentials.username,
            password: credentials.password,
          }),
        });

        const result = await response.json();

        if (!response.ok) {
          return { success: false, message: result.message || '登录失败' };
        }

        // Store session info
        if (result.session && result.session.access_token) {
          localStorage.setItem('auth_token', result.session.access_token);
          localStorage.setItem('refresh_token', result.session.refresh_token);
        }

        // Use the user data from API response
        userData = result.user;

      } catch (authError) {
        console.error('Auth error:', authError);
        return { success: false, message: '登录请求失败' };
      }

      // Update last login
      await db.users.update(userData.id, {
        last_login: new Date().toISOString()
      });

      // Log the login
      await db.logs.create({
        user_id: userData.id,
        action: '用户登录',
        details: {
          login_method: 'password',
          user_role: userData.role,
          user_name: userData.name,
        },
        target_type: 'auth',
      });

      setUser(userData);
      toast.success('登录成功！');
      return { success: true, user: userData };

    } catch (error) {
      console.error('Login error:', error);
      return { success: false, message: error.message || '登录失败' };
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      if (user) {
        // Log the logout
        await db.logs.create({
          user_id: user.id,
          action: '用户登出',
          details: {
            logout_method: 'manual',
            user_role: user.role,
            user_name: user.name,
          },
          target_type: 'auth',
        });
      }

      // Sign out from Supabase
      await supabase.auth.signOut();
      setUser(null);
      toast.success('已安全退出');
    } catch (error) {
      console.error('Logout error:', error);
      setUser(null);
    }
  };



  const changePassword = async (passwordData) => {
    try {
      if (!user) {
        return { success: false, message: '用户未登录' };
      }

      // Verify old password first
      if (user.password_hash !== passwordData.oldPassword) {
        return { success: false, message: '原密码错误' };
      }

      // Update password in database
      await db.users.update(user.id, {
        password_hash: passwordData.newPassword,
        updated_at: new Date().toISOString()
      });

      // Update password in Supabase Auth
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      });

      if (error) {
        throw error;
      }

      // Log the password change
      await db.logs.create({
        user_id: user.id,
        action: '密码修改',
        details: {
          user_role: user.role,
          user_name: user.name,
        },
        target_type: 'auth',
      });

      toast.success('密码修改成功');
      return { success: true, message: '密码修改成功' };
    } catch (error) {
      console.error('Change password error:', error);
      return { success: false, message: error.message || '密码修改失败' };
    }
  };

  const updateProfile = async (profileData) => {
    try {
      if (!user) {
        return { success: false, message: '用户未登录' };
      }

      // Update user profile in database
      const updatedUser = await db.users.update(user.id, {
        ...profileData,
        updated_at: new Date().toISOString()
      });

      // Log the profile update
      await db.logs.create({
        user_id: user.id,
        action: '个人信息修改',
        details: {
          updated_fields: Object.keys(profileData),
          user_role: user.role,
          user_name: user.name,
        },
        target_type: 'profile',
      });

      setUser(updatedUser);
      toast.success('个人信息更新成功');
      return { success: true, user: updatedUser };
    } catch (error) {
      console.error('Update profile error:', error);
      return { success: false, message: error.message || '更新失败' };
    }
  };

  const refreshUser = async () => {
    try {
      if (!user) return;

      // Refresh user data from database
      const refreshedUser = await db.users.getById(user.id);
      setUser(refreshedUser);
    } catch (error) {
      console.error('Refresh user error:', error);
    }
  };

  const value = {
    user,
    loading,
    login,
    logout,
    changePassword,
    updateProfile,
    refreshUser,
    checkAuthStatus
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
