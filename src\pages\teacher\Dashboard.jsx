import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import reportsService from '../../services/reports';
import toast from 'react-hot-toast';

const Dashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalCertificates: 0,
    pendingCertificates: 0,
    approvedCertificates: 0,
    rejectedCertificates: 0,
    recentActivities: []
  });

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Get real dashboard statistics
      const dashboardStats = await reportsService.getDashboardStats(null, 'teacher');
      setStats(dashboardStats);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('获取数据失败');

      // Fallback to mock data
      setStats({
        totalStudents: 45,
        totalCertificates: 128,
        pendingCertificates: 12,
        approvedCertificates: 98,
        rejectedCertificates: 18,
        totalCategories: 6,
        recentActivities: [
          {
            id: 1,
            action: '证书提交',
            user_name: '张三',
            details: { certificate_name: '全国大学生数学建模竞赛' },
            created_at: '2024-01-07T14:30:00Z',
            target_type: 'certificate'
          },
          {
            id: 2,
            action: '证书审核',
            user_name: '李四',
            details: { certificate_name: '程序设计竞赛', audit_status: 'approved' },
            created_at: '2024-01-07T13:15:00Z',
            target_type: 'certificate'
          },
          {
            id: 3,
            action: '用户登录',
            user_name: '王五',
            details: {},
            created_at: '2024-01-07T10:20:00Z',
            target_type: 'auth'
          }
        ]
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner text="加载工作台数据中..." />;
  }

  const StatCard = ({ title, value, icon, color = 'blue', trend }) => (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <span className="text-2xl">{icon}</span>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                {title}
              </dt>
              <dd className={`text-lg font-medium text-${color}-600`}>
                {value}
              </dd>
              {trend && (
                <dd className="text-xs text-gray-400 mt-1">
                  {trend}
                </dd>
              )}
            </dl>
          </div>
        </div>
      </div>
    </div>
  );

  const getActivityIcon = (type) => {
    const icons = {
      certificate_submitted: '📜',
      certificate_approved: '✅',
      certificate_rejected: '❌',
      student_registered: '👤',
      score_updated: '📊'
    };
    return icons[type] || '📋';
  };

  const getActivityColor = (status) => {
    const colors = {
      pending: 'text-yellow-600',
      approved: 'text-green-600',
      rejected: 'text-red-600',
      info: 'text-blue-600'
    };
    return colors[status] || 'text-gray-600';
  };

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-2xl">👨‍🏫</span>
            </div>
          </div>
          <div className="ml-6">
            <h1 className="text-2xl font-bold text-gray-900">
              欢迎回来，{user?.name || user?.username}！
            </h1>
            <p className="text-gray-600">
              教师工作台 | 学生素质测评系统管理端
            </p>
            <p className="text-sm text-gray-500 mt-1">
              上次登录：{new Date().toLocaleString()}
            </p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-5">
        <StatCard
          title="学生总数"
          value={stats.totalStudents}
          icon="👥"
          color="blue"
          trend="本学期"
        />
        <StatCard
          title="证书总数"
          value={stats.totalCertificates}
          icon="📜"
          color="purple"
          trend="累计提交"
        />
        <StatCard
          title="待审核"
          value={stats.pendingCertificates}
          icon="⏳"
          color="yellow"
          trend="需要处理"
        />
        <StatCard
          title="已通过"
          value={stats.approvedCertificates}
          icon="✅"
          color="green"
          trend="审核通过"
        />
        <StatCard
          title="已拒绝"
          value={stats.rejectedCertificates}
          icon="❌"
          color="red"
          trend="审核拒绝"
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            快速操作
          </h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <a
              href="/teacher/audit"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-yellow-50 text-yellow-700 ring-4 ring-white">
                  <span className="text-xl">✅</span>
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true"></span>
                  证书审核
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  {stats.pendingCertificates}个待审核
                </p>
              </div>
            </a>

            <a
              href="/teacher/students"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                  <span className="text-xl">👥</span>
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true"></span>
                  学生管理
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  管理{stats.totalStudents}名学生
                </p>
              </div>
            </a>

            <a
              href="/teacher/reports"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                  <span className="text-xl">📊</span>
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true"></span>
                  成绩报表
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  导出统计报表
                </p>
              </div>
            </a>

            <a
              href="/teacher/settings"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                  <span className="text-xl">⚙️</span>
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true"></span>
                  系统设置
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  配置系统参数
                </p>
              </div>
            </a>
          </div>
        </div>
      </div>

      {/* Recent Activities */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            最近活动
          </h3>
          
          {stats.recentActivities.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <span className="text-4xl mb-4 block">📋</span>
              <p>暂无最近活动</p>
            </div>
          ) : (
            <div className="flow-root">
              <ul className="-mb-8">
                {stats.recentActivities.map((activity, index) => (
                  <li key={activity.id}>
                    <div className="relative pb-8">
                      {index !== stats.recentActivities.length - 1 && (
                        <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
                      )}
                      <div className="relative flex space-x-3">
                        <div>
                          <span className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center ring-8 ring-white">
                            <span className="text-sm">{getActivityIcon(activity.type)}</span>
                          </span>
                        </div>
                        <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p className="text-sm text-gray-500">
                              {activity.type === 'certificate_submitted' && (
                                <>
                                  <span className="font-medium text-gray-900">{activity.student_name}</span>
                                  {' '}提交了证书{' '}
                                  <span className="font-medium text-gray-900">{activity.certificate_name}</span>
                                </>
                              )}
                              {activity.type === 'certificate_approved' && (
                                <>
                                  <span className="font-medium text-gray-900">{activity.student_name}</span>
                                  {' '}的证书{' '}
                                  <span className="font-medium text-gray-900">{activity.certificate_name}</span>
                                  {' '}已通过审核
                                </>
                              )}
                              {activity.type === 'student_registered' && (
                                <>
                                  新学生{' '}
                                  <span className="font-medium text-gray-900">{activity.student_name}</span>
                                  {' '}已注册
                                </>
                              )}
                            </p>
                          </div>
                          <div className="text-right text-sm whitespace-nowrap text-gray-500">
                            <time>{activity.time}</time>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* System Status */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <span className="text-green-400 text-xl">✅</span>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-green-800">
              系统运行正常
            </h3>
            <div className="mt-2 text-sm text-green-700">
              <p>数据库连接正常 | 文件存储正常 | 所有服务运行中</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
