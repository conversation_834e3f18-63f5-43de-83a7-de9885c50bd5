# 🎓 学生素质测评系统

<div align="center">

![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)
![React](https://img.shields.io/badge/React-18-blue.svg)
![Supabase](https://img.shields.io/badge/Supabase-PostgreSQL-green.svg)
![Vercel](https://img.shields.io/badge/Vercel-Deployed-black.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)
![Status](https://img.shields.io/badge/Status-Production%20Ready-success.svg)

现代化的学生素质测评管理系统，支持证书提交、智能审核、成绩统计等完整功能。
**已完成现代化升级：React + Vite + Tailwind CSS + Supabase + Vercel**

[🚀 现代版本部署指南](./NEXT_STEPS.md) | [📊 部署状态](./DEPLOYMENT_STATUS.md) | [📖 详细文档](./deployment-guide.md)

</div>

## 🚀 系统特性

### ✨ 核心功能
- **🎓 证书管理**: 学生提交证书，教师审核评分
- **📊 成绩统计**: 自动计算总分，生成统计报表
- **👥 用户管理**: 三级权限控制（学生/教师/管理员）
- **📁 文件存储**: 云端存储，支持多种文件格式
- **📈 数据分析**: 可视化图表，Excel导入导出
- **🔐 安全认证**: JWT Token + 权限验证

### 🏗️ 技术架构
- **前端**: React 18 + Vite + Tailwind CSS
- **后端**: Vercel Functions (无服务器)
- **数据库**: Supabase PostgreSQL
- **部署**: Vercel + 全球CDN
- **存储**: Supabase Storage

## 📋 目录

- [功能特性](#功能特性)
- [技术栈](#技术栈)
- [系统架构](#系统架构)
- [快速开始](#快速开始)
- [详细安装指南](#详细安装指南)
- [使用说明](#使用说明)
- [API文档](#api文档)
- [项目结构](#项目结构)
- [配置说明](#配置说明)
- [部署指南](#部署指南)
- [常见问题](#常见问题)
- [贡献指南](#贡献指南)
- [更新日志](#更新日志)
- [许可证](#许可证)

## ✨ 功能特性

### 🎓 学生端功能
- **📊 个人成绩查看**
  - 实时查看个人综合素质测评成绩
  - 分类别查看各项得分详情
  - 成绩趋势图表展示
  - 排名信息显示

- **📜 证书管理**
  - 在线上传各类证书文件
  - 实时查看审核状态
  - 证书分类管理
  - 历史记录查询

- **🔐 账户管理**
  - 安全密码修改
  - 个人信息维护
  - 登录历史查看

- **📈 数据统计**
  - 个人数据可视化
  - 学期对比分析
  - 成长轨迹展示

### 👨‍🏫 教师端功能
- **👥 学生管理**
  - 学生信息批量导入/导出
  - 班级管理和分组
  - 学生档案维护
  - 批量操作支持

- **✅ 证书审核**
  - 在线证书审核流程
  - 批量审核操作
  - 审核意见记录
  - 审核统计报表

- **📊 成绩管理**
  - 多维度成绩录入
  - 自动计算综合分数
  - 成绩分析报告
  - 排名统计功能

- **📤 数据导入导出**
  - Excel批量导入学生信息
  - 成绩数据批量导出
  - 模板下载功能
  - 数据验证机制

- **🏆 审核活动管理**
  - 创建和管理审核活动
  - 活动时间控制
  - 参与人员管理
  - 活动统计分析

- **👨‍💼 权限管理**
  - 多级权限控制
  - 管理员角色分配
  - 操作权限细分
  - 安全访问控制

- **📋 系统监控**
  - 详细操作日志
  - 系统使用统计
  - 安全事件监控
  - 性能指标展示

## 🛠 技术栈

### 前端技术
- **React 18** - 现代化前端框架
- **Ant Design 5** - 企业级UI组件库
- **React Router 6** - 单页应用路由
- **Axios** - HTTP客户端
- **Vite** - 快速构建工具
- **JavaScript ES6+** - 现代JavaScript语法

### 后端技术
- **Node.js 16+** - 服务器运行环境
- **Express.js** - Web应用框架
- **JWT** - 身份认证机制
- **Multer** - 文件上传处理
- **JSON** - 轻量级数据存储
- **CORS** - 跨域资源共享

### 开发工具
- **Git** - 版本控制
- **npm** - 包管理器
- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化

## 🏗 系统架构

```
┌─────────────────┐    HTTP/HTTPS    ┌─────────────────┐
│   前端 (React)   │ ◄──────────────► │  后端 (Node.js)  │
│                 │                  │                 │
│  - 用户界面      │                  │  - API服务       │
│  - 状态管理      │                  │  - 身份认证      │
│  - 路由控制      │                  │  - 文件处理      │
└─────────────────┘                  └─────────────────┘
                                              │
                                              ▼
                                     ┌─────────────────┐
                                     │   数据存储       │
                                     │                 │
                                     │  - JSON文件      │
                                     │  - 文件上传      │
                                     │  - 日志记录      │
                                     └─────────────────┘
```

## 🚀 快速开始

### 📋 环境要求

| 软件 | 版本要求 | 说明 |
|------|----------|------|
| Node.js | >= 16.0.0 | JavaScript运行环境 |
| npm | >= 8.0.0 | 包管理器 |
| Git | 最新版本 | 版本控制工具 |

### ⚡ 一键启动

```bash
# 1. 克隆项目
git clone https://github.com/ruiyan886/henurjxylyhsanping.git
cd henurjxylyhsanping

# 2. 一键启动（Windows）
start.bat

# 或者 PowerShell
.\start.ps1
```

### 🔧 手动安装

如果您需要手动安装和配置：

```bash
# 1. 克隆项目
git clone https://github.com/ruiyan886/henurjxylyhsanping.git
cd henurjxylyhsanping

# 2. 安装后端依赖
cd server
npm install

# 3. 安装前端依赖
cd ../client
npm install

# 4. 启动后端服务器
cd ../server
npm start

# 5. 启动前端开发服务器（新终端窗口）
cd ../client
npm run dev
```

### 🌐 访问系统

启动成功后，您可以通过以下地址访问系统：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:5000
- **API文档**: http://localhost:5000/api-docs （如果启用）

### 👤 默认账户

系统预置了以下测试账户：

#### 系统账户
```
请联系系统管理员获取登录凭据
默认情况下，学生使用学号作为用户名和密码
教师和管理员账户由系统管理员分配
```

## 📖 详细安装指南

### Windows 系统

1. **安装 Node.js**
   - 访问 [Node.js官网](https://nodejs.org/)
   - 下载并安装 LTS 版本
   - 验证安装：`node --version` 和 `npm --version`

2. **克隆项目**
   ```cmd
   git clone https://github.com/ruiyan886/henurjxylyhsanping.git
   cd henurjxylyhsanping
   ```

3. **使用启动脚本**
   ```cmd
   # 双击运行或命令行执行
   start.bat
   ```

### Linux/Mac 系统

1. **安装 Node.js**
   ```bash
   # Ubuntu/Debian
   curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
   sudo apt-get install -y nodejs

   # macOS (使用 Homebrew)
   brew install node
   ```

2. **克隆和启动**
   ```bash
   git clone https://github.com/ruiyan886/henurjxylyhsanping.git
   cd henurjxylyhsanping
   chmod +x start.sh
   ./start.sh
   ```

### Docker 部署（可选）

```bash
# 构建镜像
docker build -t student-assessment .

# 运行容器
docker run -p 3000:3000 -p 5000:5000 student-assessment
```

## 📚 使用说明

### 学生端操作流程

1. **登录系统**
   - 使用学号和密码登录
   - 首次登录建议修改密码

2. **查看成绩**
   - 在首页查看综合成绩概览
   - 点击"我的成绩"查看详细分数

3. **上传证书**
   - 进入"证书管理"页面
   - 点击"上传证书"按钮
   - 选择证书类别和上传文件
   - 等待教师审核

4. **修改密码**
   - 进入"修改密码"页面
   - 输入原密码和新密码
   - 确认修改

### 教师端操作流程

1. **学生管理**
   - 批量导入学生信息
   - 编辑学生基本信息
   - 管理班级分组

2. **证书审核**
   - 查看待审核证书列表
   - 点击证书查看详情
   - 选择"通过"或"拒绝"
   - 填写审核意见

3. **成绩管理**
   - 录入各类成绩
   - 查看成绩统计
   - 导出成绩报表

4. **系统管理**
   - 管理用户权限
   - 查看操作日志
   - 系统设置配置

## 🔌 API文档

### 认证接口

| 方法 | 路径 | 说明 |
|------|------|------|
| POST | `/api/auth/login` | 用户登录 |
| POST | `/api/auth/logout` | 用户登出 |
| GET | `/api/auth/current` | 获取当前用户信息 |

### 学生管理接口

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/students` | 获取学生列表 |
| POST | `/api/students` | 创建学生 |
| PUT | `/api/students/:id` | 更新学生信息 |
| DELETE | `/api/students/:id` | 删除学生 |

### 证书管理接口

| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/certificates` | 获取证书列表 |
| POST | `/api/certificates` | 上传证书 |
| PUT | `/api/certificates/:id` | 审核证书 |
| DELETE | `/api/certificates/:id` | 删除证书 |

## 📁 项目结构

```
henurjxylyhsanping/
├── 📁 client/                    # 前端React应用
│   ├── 📁 public/               # 静态资源
│   ├── 📁 src/                  # 源代码
│   │   ├── 📁 components/       # 公共组件
│   │   ├── 📁 pages/           # 页面组件
│   │   │   ├── 📁 student/     # 学生端页面
│   │   │   └── 📁 teacher/     # 教师端页面
│   │   ├── 📁 utils/           # 工具函数
│   │   │   └── 📄 api.js       # API接口封装
│   │   ├── 📄 App.jsx          # 主应用组件
│   │   ├── 📄 main.jsx         # 应用入口
│   │   └── 📄 index.css        # 全局样式
│   ├── 📄 package.json         # 前端依赖配置
│   ├── 📄 vite.config.js       # Vite构建配置
│   └── 📄 index.html           # HTML模板
├── 📁 server/                   # 后端Node.js应用
│   ├── 📄 sqlite-server.js     # 主服务器文件
│   ├── 📄 data.json            # 数据存储文件
│   ├── 📁 uploads/             # 文件上传目录
│   ├── 📄 package.json         # 后端依赖配置
│   └── 📄 .env.example         # 环境变量示例
├── 📄 start.bat                # Windows启动脚本
├── 📄 start.ps1                # PowerShell启动脚本
├── 📄 stop.bat                 # Windows停止脚本
├── 📄 .gitignore               # Git忽略文件
├── 📄 README.md                # 项目说明文档
├── 📄 系统使用说明.md           # 详细使用说明
├── 📄 素质测评成绩计算细则.md    # 成绩计算规则
└── 📄 项目说明.md               # 项目详细说明
```

## ⚙️ 配置说明

### 环境变量配置

在 `server` 目录下创建 `.env` 文件：

```env
# 服务器配置
PORT=5000
NODE_ENV=development

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# 文件上传配置
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,pdf,doc,docx

# 安全配置
RATE_LIMIT_WINDOW=60000   # 1分钟
RATE_LIMIT_MAX=100        # 最大请求数
```

### 前端配置

修改 `client/vite.config.js`：

```javascript
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true
      }
    }
  }
})
```

## 🚀 部署指南

### 生产环境部署

1. **构建前端应用**
   ```bash
   cd client
   npm run build
   ```

2. **配置生产环境**
   ```bash
   cd server
   cp .env.example .env
   # 编辑 .env 文件，设置生产环境变量
   ```

3. **启动生产服务**
   ```bash
   npm run start:prod
   ```

### 使用PM2部署

```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs
```

### Nginx反向代理配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔒 安全特性

- **🔐 JWT Token认证** - 安全的用户身份验证
- **🚦 请求频率限制** - 防止API滥用和DDoS攻击
- **🛡️ 输入验证和清理** - 防止SQL注入和XSS攻击
- **📁 文件上传安全检查** - 限制文件类型和大小
- **📋 详细操作日志** - 记录所有用户操作
- **👥 细粒度权限管理** - 多级权限控制系统
- **🔒 密码加密存储** - 安全的密码处理机制
- **🚫 CORS配置** - 跨域请求安全控制

## ❓ 常见问题

### Q: 启动时提示端口被占用怎么办？
A: 检查端口占用情况，修改配置文件中的端口号，或停止占用端口的程序。

```bash
# Windows查看端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :5000

# 结束进程
taskkill /PID <进程ID> /F
```

### Q: 上传文件失败怎么办？
A: 检查文件大小是否超过限制（默认10MB），文件格式是否支持，服务器uploads目录是否有写入权限。

### Q: 忘记登录密码怎么办？
A: 请联系系统管理员重置密码。

### Q: 如何备份数据？
A: 定期备份 `server/data.json` 文件和 `server/uploads/` 目录即可。

## 🤝 贡献指南

我们欢迎所有形式的贡献！请遵循以下步骤：

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送到分支** (`git push origin feature/AmazingFeature`)
5. **创建 Pull Request**

### 代码规范

- 使用 ESLint 进行代码检查
- 遵循 Prettier 代码格式化规则
- 编写清晰的提交信息
- 添加必要的注释和文档

## 📝 更新日志

### v1.0.0 (2025-01-06)
- ✨ 初始版本发布
- 🎓 完整的学生端功能
- 👨‍🏫 完整的教师端功能
- 🔒 安全认证系统
- 📊 数据统计功能

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

- **项目维护者**: ruiyan886
- **邮箱**: <EMAIL>
- **GitHub**: https://github.com/ruiyan886
- **项目地址**: https://github.com/ruiyan886/henurjxylyhsanping

如有问题或建议，请通过以下方式联系：
- 📧 发送邮件
- 🐛 提交 GitHub Issues
- 💡 提交 Feature Request

---

## 📊 项目状态

- **版本**: v2.0.1 - 生产优化版
- **状态**: 🚀 生产就绪，可立即部署
- **GitHub**: ✅ 最新代码已更新 (2025-01-07 21:35)
- **Vercel配置**: ✅ 部署错误已修复
- **数据库**: ✅ Supabase PostgreSQL 配置完成
- **存储**: ✅ Supabase Storage 桶已创建

### 🔄 最新更新
- 修复了 Vercel 部署配置中的 `functions` 和 `builds` 属性冲突
- 优化了项目结构，删除了冗余文件
- 完善了部署文档和状态报告
- 所有组件已就绪，可立即进行生产部署

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**

Made with ❤️ by [ruiyan886](https://github.com/ruiyan886)

</div>
