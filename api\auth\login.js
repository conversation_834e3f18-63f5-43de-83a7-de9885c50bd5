import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

// Initialize Supabase client with error handling
let supabase;
try {
  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error('Missing Supabase environment variables');
  }

  supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  );
} catch (error) {
  console.error('Supabase initialization error:', error);
}

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Check if Supabase is initialized
  if (!supabase) {
    return res.status(500).json({
      message: '数据库连接失败',
      error: 'Supabase not initialized'
    });
  }

  try {
    const { student_id, password } = req.body;

    // Input validation
    if (!student_id || !password) {
      return res.status(400).json({ message: '账号和密码不能为空' });
    }

    // Security check - basic input sanitization
    const cleanStudentId = String(student_id).trim();
    const cleanPassword = String(password).trim();

    if (!cleanStudentId || !cleanPassword) {
      return res.status(400).json({ message: '账号和密码格式不正确' });
    }

    // Find user by student_id or username
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .or(`student_id.eq."${cleanStudentId}",username.eq."${cleanStudentId}"`)
      .single();

    if (userError) {
      console.log('Database query error:', userError);
      return res.status(500).json({
        message: '数据库查询失败',
        error: userError.message
      });
    }

    if (!userData) {
      console.log('User not found:', cleanStudentId);
      return res.status(401).json({ message: '账号或密码错误' });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(cleanPassword, userData.password_hash);

    if (!isPasswordValid) {
      console.log('Invalid password for user:', cleanStudentId);
      return res.status(401).json({ message: '账号或密码错误' });
    }

    // Update last login time
    try {
      await supabase
        .from('users')
        .update({ last_login: new Date().toISOString() })
        .eq('id', userData.id);
    } catch (updateError) {
      console.log('Failed to update last login:', updateError);
      // Don't fail login for this
    }

    // Generate simple session token (JWT-like structure)
    const sessionToken = Buffer.from(JSON.stringify({
      user_id: userData.id,
      student_id: userData.student_id,
      role: userData.role,
      exp: Date.now() + (3600 * 1000) // 1 hour
    })).toString('base64');

    // Return user data and session info
    res.status(200).json({
      message: '登录成功',
      user: {
        id: userData.id,
        student_id: userData.student_id,
        username: userData.username,
        name: userData.name,
        class_name: userData.class_name,
        role: userData.role,
        permissions: userData.permissions || [],
        is_super_admin: userData.is_super_admin || false
      },
      session: {
        access_token: sessionToken,
        expires_at: new Date(Date.now() + (3600 * 1000)).toISOString()
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    return res.status(500).json({
      message: '系统错误，请稍后重试',
      error: error.message
    });
  }
}
