import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const { student_id, password } = req.body;

    // Input validation
    if (!student_id || !password) {
      return res.status(400).json({ message: '账号和密码不能为空' });
    }

    // Security check - basic input sanitization
    const cleanStudentId = student_id.trim();
    const cleanPassword = password.trim();

    if (!cleanStudentId || !cleanPassword) {
      return res.status(400).json({ message: '账号和密码格式不正确' });
    }

    // Find user by student_id or username
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .or(`student_id.eq.${cleanStudentId},username.eq.${cleanStudentId}`)
      .single();

    if (userError || !userData) {
      console.log('User not found:', cleanStudentId);
      return res.status(401).json({ message: '账号或密码错误' });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(cleanPassword, userData.password_hash);
    
    if (!isPasswordValid) {
      console.log('Invalid password for user:', cleanStudentId);
      
      // Log failed login attempt
      await supabase
        .from('operation_logs')
        .insert({
          user_id: userData.id,
          action: '登录失败',
          details: {
            reason: 'wrong_password',
            ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']?.substring(0, 100)
          },
          target_type: 'auth',
          ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
          user_agent: req.headers['user-agent']
        });

      return res.status(401).json({ message: '账号或密码错误' });
    }

    // Create auth session using Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: `${userData.student_id}@system.local`,
      options: {
        data: {
          student_id: userData.student_id,
          name: userData.name,
          role: userData.role,
          user_id: userData.id
        }
      }
    });

    if (authError) {
      console.error('Auth error:', authError);
      return res.status(500).json({ message: '认证服务错误' });
    }

    // Update last login time
    await supabase
      .from('users')
      .update({ last_login: new Date().toISOString() })
      .eq('id', userData.id);

    // Log successful login
    await supabase
      .from('operation_logs')
      .insert({
        user_id: userData.id,
        action: '用户登录',
        details: {
          login_method: 'password',
          user_role: userData.role,
          user_name: userData.name
        },
        target_type: 'auth',
        ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        user_agent: req.headers['user-agent']
      });

    // Return user data and session info
    res.status(200).json({
      message: '登录成功',
      user: {
        id: userData.id,
        student_id: userData.student_id,
        username: userData.username,
        name: userData.name,
        class_name: userData.class_name,
        role: userData.role,
        permissions: userData.permissions || [],
        is_super_admin: userData.is_super_admin || false
      },
      session: {
        access_token: authData.properties?.access_token,
        refresh_token: authData.properties?.refresh_token,
        expires_at: authData.properties?.expires_at
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    
    // Log system error
    await supabase
      .from('operation_logs')
      .insert({
        user_id: null,
        action: '登录系统错误',
        details: {
          error: error.message,
          ip: req.headers['x-forwarded-for'] || req.connection.remoteAddress
        },
        target_type: 'system',
        ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        user_agent: req.headers['user-agent']
      });

    return res.status(500).json({ message: '系统错误，请稍后重试' });
  }
}
