import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 配置 Supabase 客户端
const supabaseUrl = process.env.SUPABASE_URL?.trim();
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY?.trim();

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('请设置 SUPABASE_URL 和 SUPABASE_SERVICE_ROLE_KEY 环境变量');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createAdminUser() {
  try {
    console.log('🔍 检查是否已存在管理员用户...');
    
    // 检查是否已存在管理员用户
    const { data: existingAdmin, error: checkError } = await supabase
      .from('users')
      .select('*')
      .or('username.eq."admin",student_id.eq."admin"')
      .single();

    if (existingAdmin) {
      console.log('✅ 管理员用户已存在:');
      console.log('- ID:', existingAdmin.id);
      console.log('- 用户名:', existingAdmin.username);
      console.log('- 学号:', existingAdmin.student_id);
      console.log('- 姓名:', existingAdmin.name);
      console.log('- 角色:', existingAdmin.role);
      console.log('- 是否超级管理员:', existingAdmin.is_super_admin);
      return existingAdmin;
    }

    console.log('📝 创建管理员用户...');
    
    // 创建管理员用户
    const adminPassword = 'admin123';
    const passwordHash = await bcrypt.hash(adminPassword, 10);

    const adminUser = {
      student_id: 'admin',
      username: 'admin',
      name: '系统管理员',
      class_name: null,
      password_hash: passwordHash,
      role: 'admin',
      permissions: [
        'student_management',
        'teacher_management', 
        'certificate_management',
        'system_management',
        'audit_management'
      ],
      is_super_admin: true,
      created_at: new Date().toISOString(),
      last_login: null
    };

    const { data: newAdmin, error: createError } = await supabase
      .from('users')
      .insert(adminUser)
      .select()
      .single();

    if (createError) {
      console.error('❌ 创建管理员用户失败:', createError);
      throw createError;
    }

    console.log('✅ 管理员用户创建成功!');
    console.log('- ID:', newAdmin.id);
    console.log('- 用户名:', newAdmin.username);
    console.log('- 学号:', newAdmin.student_id);
    console.log('- 姓名:', newAdmin.name);
    console.log('- 角色:', newAdmin.role);
    console.log('- 默认密码:', adminPassword);
    console.log('');
    console.log('🎯 登录信息:');
    console.log('- 用户名: admin');
    console.log('- 密码: admin123');

    return newAdmin;

  } catch (error) {
    console.error('❌ 创建管理员用户时出错:', error);
    throw error;
  }
}

// 执行创建管理员用户
createAdminUser()
  .then(() => {
    console.log('🎉 管理员用户设置完成!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 设置失败:', error);
    process.exit(1);
  });
