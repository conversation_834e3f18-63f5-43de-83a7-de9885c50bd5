import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import reportsService from '../../services/reports';
import apiService from '../../services/api';
import toast from 'react-hot-toast';
import * as XLSX from 'xlsx';

const Reports = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [reportData, setReportData] = useState({
    summary: {
      totalStudents: 0,
      averageScore: 0,
      totalCertificates: 0,
      passRate: 0
    },
    classStats: [],
    categoryStats: [],
    topStudents: []
  });
  const [selectedClass, setSelectedClass] = useState('all');
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    fetchReportData();
  }, [selectedClass, dateRange]);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Prepare filters
      const filters = {
        class: selectedClass !== 'all' ? selectedClass : null,
        startDate: dateRange.startDate || null,
        endDate: dateRange.endDate || null
      };

      // Get report data using reports service
      const data = await reportsService.getReportData(filters);
      setReportData(data);
    } catch (error) {
      console.error('Error fetching report data:', error);
      toast.error('获取报表数据失败');

      // Fallback to mock data
      setReportData({
        summary: {
          totalStudents: 45,
          averageScore: 82.3,
          totalCertificates: 128,
          passRate: 95.6
        },
        classStats: [
          {
            class_name: '体育教育21-1班',
            student_count: 25,
            average_score: 85.2,
            certificate_count: 78,
            pass_rate: 96.0
          },
          {
            class_name: '体育教育21-2班',
            student_count: 20,
            average_score: 78.9,
            certificate_count: 50,
            pass_rate: 95.0
          }
        ],
        categoryStats: [
          {
            category_name: '专业类活动',
            certificate_count: 45,
            total_score: 360,
            average_score: 8.0
          },
          {
            category_name: '体育美育综合活动',
            certificate_count: 38,
            total_score: 190,
            average_score: 5.0
          },
          {
            category_name: '文明品德综合活动',
            certificate_count: 45,
            total_score: 225,
            average_score: 5.0
          }
        ],
        topStudents: [
          {
            name: '王五',
            student_id: '2021003',
            class_name: '体育教育21-2班',
            total_score: 92.3,
            certificate_count: 8
          },
          {
            name: '张三',
            student_id: '2021001',
            class_name: '体育教育21-1班',
            total_score: 85.5,
            certificate_count: 5
          },
          {
            name: '李四',
            student_id: '2021002',
            class_name: '体育教育21-1班',
            total_score: 78.0,
            certificate_count: 3
          }
        ]
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExportExcel = async () => {
    setExporting(true);
    try {
      // Generate filename with current date and filters
      let filename = `学生成绩报表_${new Date().toISOString().split('T')[0]}`;
      if (selectedClass) {
        filename += `_${selectedClass}`;
      }
      if (selectedCategory) {
        filename += `_${selectedCategory}`;
      }

      // Export using reports service
      await reportsService.exportToExcel(reportData, filename);
    } catch (error) {
      console.error('Export error:', error);
      // Error handling is done in the reports service
    } finally {
      setExporting(false);
    }
  };

  // Export detailed certificate records
  const handleExportCertificates = async () => {
    setExporting(true);
    try {
      // Get all certificates with detailed information
      const certificates = await apiService.certificates.getAll();

      const exportData = certificates.map(cert => ({
        '学号': cert.student_id,
        '姓名': cert.student_name,
        '班级': cert.class_name,
        '证书名称': cert.certificate_name,
        '证书类别': cert.category_name,
        '获奖等级': cert.level,
        '获奖日期': cert.award_date,
        '团队人数': cert.team_size || '个人',
        '项目角色': cert.role || '-',
        '得分': cert.score || 0,
        '审核状态': cert.status === 'approved' ? '已通过' : cert.status === 'rejected' ? '未通过' : '待审核',
        '审核时间': cert.audit_date ? new Date(cert.audit_date).toLocaleDateString() : '-',
        '审核意见': cert.comments || '-'
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '证书记录');

      // Set column widths
      const colWidths = [
        { wch: 12 }, // 学号
        { wch: 10 }, // 姓名
        { wch: 20 }, // 班级
        { wch: 25 }, // 证书名称
        { wch: 15 }, // 证书类别
        { wch: 15 }, // 获奖等级
        { wch: 12 }, // 获奖日期
        { wch: 10 }, // 团队人数
        { wch: 10 }, // 项目角色
        { wch: 8 },  // 得分
        { wch: 10 }, // 审核状态
        { wch: 12 }, // 审核时间
        { wch: 30 }  // 审核意见
      ];
      ws['!cols'] = colWidths;

      XLSX.writeFile(wb, `证书记录详情_${new Date().toISOString().split('T')[0]}.xlsx`);
      toast.success('证书记录导出成功');
    } catch (error) {
      console.error('Export certificates error:', error);
      toast.error('证书记录导出失败');
    } finally {
      setExporting(false);
    }
  };

  if (loading) {
    return <LoadingSpinner text="加载报表数据中..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">成绩报表</h1>
        <div className="flex space-x-3">
          <button
            onClick={handleExportCertificates}
            disabled={exporting}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
          >
            {exporting ? '导出中...' : '📋 导出证书记录'}
          </button>
          <button
            onClick={handleExportExcel}
            disabled={exporting}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
          >
            {exporting ? '导出中...' : '📊 导出成绩报表'}
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">班级筛选</label>
            <select
              value={selectedClass}
              onChange={(e) => setSelectedClass(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">全部班级</option>
              <option value="体育教育21-1班">体育教育21-1班</option>
              <option value="体育教育21-2班">体育教育21-2班</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">开始日期</label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">结束日期</label>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">👥</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">学生总数</dt>
                  <dd className="text-lg font-medium text-gray-900">{reportData.summary.totalStudents}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">平均分</dt>
                  <dd className="text-lg font-medium text-blue-600">{reportData.summary.averageScore}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📜</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">证书总数</dt>
                  <dd className="text-lg font-medium text-purple-600">{reportData.summary.totalCertificates}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">通过率</dt>
                  <dd className="text-lg font-medium text-green-600">{reportData.summary.passRate}%</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Class Statistics */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            班级统计
          </h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    班级
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    学生数
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    平均分
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    证书数
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    通过率
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.classStats.map((classData, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {classData.class_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {classData.student_count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-blue-600 font-medium">
                      {classData.average_score}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {classData.certificate_count}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600 font-medium">
                      {classData.pass_rate}%
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Category Statistics */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            证书类别统计
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {reportData.categoryStats.map((category, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{category.category_name}</h4>
                  <span className="text-2xl">
                    {index === 0 ? '📚' : index === 1 ? '🏃‍♂️' : '🌟'}
                  </span>
                </div>
                <div className="space-y-1 text-sm text-gray-600">
                  <p>证书数量: <span className="font-medium">{category.certificate_count}</span></p>
                  <p>总分值: <span className="font-medium">{category.total_score}</span></p>
                  <p>平均分: <span className="font-medium text-blue-600">{category.average_score}</span></p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Top Students */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            优秀学生排行
          </h3>
          <div className="space-y-3">
            {reportData.topStudents.map((student, index) => (
              <div key={student.student_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center">
                  <div className="flex-shrink-0 mr-4">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                      index === 0 ? 'bg-yellow-500' : index === 1 ? 'bg-gray-400' : index === 2 ? 'bg-orange-500' : 'bg-blue-500'
                    }`}>
                      {index + 1}
                    </div>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{student.name}</p>
                    <p className="text-sm text-gray-600">
                      {student.student_id} · {student.class_name}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-semibold text-blue-600">{student.total_score}分</p>
                  <p className="text-sm text-gray-500">{student.certificate_count}个证书</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Reports;
