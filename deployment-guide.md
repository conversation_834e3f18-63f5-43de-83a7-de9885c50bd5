# 🚀 学生素质测评系统 - 生产部署指南

## 🎯 项目概述

现代化的学生素质测评管理系统，支持完整的学生素质评估流程。

### 🏗️ 技术架构
- **前端**: React 18 + Vite + Tailwind CSS + Headless UI
- **后端**: Vercel Functions (无服务器架构)
- **数据库**: Supabase PostgreSQL (云数据库)
- **认证**: Supabase Auth + JWT Token
- **存储**: Supabase Storage (文件存储)
- **部署**: Vercel (全球 CDN)

### ✨ 核心功能
- 🎓 学生证书提交和管理
- 👨‍🏫 教师审核和成绩管理
- 📊 Excel 批量导入导出
- 👥 用户权限管理 (学生/教师/管理员)
- 📝 操作日志记录
- 📈 成绩统计和分析

## 📋 部署前准备

### 1. 环境要求
```bash
Node.js >= 18.0.0
npm >= 8.0.0
Git >= 2.0.0
```

### 2. 必需账号
- ✅ [Supabase](https://supabase.com) - 数据库和认证
- ✅ [Vercel](https://vercel.com) - 应用部署
- ✅ [GitHub](https://github.com) - 代码托管

## 🗄️ 第一步：Supabase 数据库设置（已完成）

### 1. Supabase 项目信息
- **项目 URL**: `https://appthjywqqzhqqxgktkr.supabase.co`
- **数据库密码**: `Lyh000000!!`
- **区域**: Southeast Asia (Singapore)
- **状态**: ✅ 已配置完成

### 2. 数据库架构（已完成）
- ✅ 6个主要数据表已创建
- ✅ UUID 主键和 JSONB 字段已配置
- ✅ 数据迁移已完成（3个证书类别，1个管理员用户）

### 3. 当前需要修复的问题
**RLS 无限递归问题**: 需要在 Supabase SQL 编辑器中执行以下修复脚本：

```sql
-- 学生素质测评系统 - RLS修复脚本
-- 删除所有现有策略并临时禁用RLS

-- 删除现有策略
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Teachers can insert users" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Super admin can delete users" ON users;
DROP POLICY IF EXISTS "Students view own certificates, teachers view all" ON certificates;
DROP POLICY IF EXISTS "Students can submit certificates" ON certificates;
DROP POLICY IF EXISTS "Certificate update policy" ON certificates;
DROP POLICY IF EXISTS "Certificate delete policy" ON certificates;
DROP POLICY IF EXISTS "Teachers can manage audit activities" ON audit_activities;
DROP POLICY IF EXISTS "All users can view audit activities" ON audit_activities;
DROP POLICY IF EXISTS "All users can view certificate categories" ON certificate_categories;
DROP POLICY IF EXISTS "Teachers can manage certificate categories" ON certificate_categories;
DROP POLICY IF EXISTS "Teachers can view operation logs" ON operation_logs;
DROP POLICY IF EXISTS "System logs operations" ON operation_logs;
DROP POLICY IF EXISTS "Teachers can manage system settings" ON system_settings;
DROP POLICY IF EXISTS "All users can view system settings" ON system_settings;

-- 临时禁用所有表的RLS
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE certificates DISABLE ROW LEVEL SECURITY;
ALTER TABLE audit_activities DISABLE ROW LEVEL SECURITY;
ALTER TABLE certificate_categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE operation_logs DISABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings DISABLE ROW LEVEL SECURITY;
```

### 4. 存储桶配置（已完成）
在 Supabase Storage 中创建：
- `certificates` 桶（用于证书文件）
- `excel-files` 桶（用于Excel导出文件）
2. 创建两个存储桶：
   - `certificates` (用于证书文件)
   - `excel-files` (用于Excel导入文件)
3. 设置存储桶为公开访问

### 4. 获取 API 密钥
1. 进入 "Settings" > "API"
2. 复制以下信息：
   - Project URL:https://appthjywqqzhqqxgktkr.supabase.co
   - anon public key:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.JESFGQJCjRFQEQH73teYa7iGVdbZyrnMh-acxWV3Uiw
   - service_role secret key:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.bchBxRPo6lh4fHllvC_8Mbwe6Cfgbl45Ym-MutRvlHI
   JWT Secret:f97dKg5usn3YpHq1E1UR1iBfNqHzSilBety0cK8uAgX63NXZkbvcpX3o2fFxOUiSiol7dwoBq1if7mtuYcTe4w==
   Access token expiry time:3600

## 🔧 第二步：本地开发环境设置

### 1. 克隆项目
```bash
git clone https://github.com/ruiyan886/henurjxylyhsanping.git
cd henurjxylyhsanping
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境变量配置
复制 `.env.example` 为 `.env` 并填写实际值：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 4. 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000 查看应用

## 🌐 第三步：Vercel 部署

### 1. 连接 GitHub 仓库
1. 访问 [Vercel Dashboard](https://vercel.com/dashboard)
2. 点击 "New Project"
3. 选择 GitHub 仓库 `henurjxylyhsanping`
4. 点击 "Import"

### 2. 配置构建设置
- Framework Preset: `Vite`
- Build Command: `npm run build`
- Output Directory: `dist`
- Install Command: `npm install`

### 3. 设置环境变量
在 Vercel 项目设置中添加环境变量：
```
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### 4. 部署项目
点击 "Deploy" 开始部署，等待构建完成。

## 🔐 第四步：安全配置

### 1. Supabase RLS 策略验证
确保所有 RLS 策略已正确应用：
```sql
-- 检查策略是否生效
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';
```

### 2. API 路由保护
确保所有 API 路由都有适当的认证检查：
- 登录验证
- 权限检查
- 输入验证
- 错误处理

### 3. 环境变量安全
- 使用 Vercel 环境变量管理敏感信息
- 不要在代码中硬编码密钥
- 定期轮换 API 密钥

## 📊 第五步：数据迁移

### 1. 导出现有数据
如果有现有的 SQLite 数据库，需要导出数据：
```bash
# 导出用户数据
sqlite3 database.db ".mode csv" ".output users.csv" "SELECT * FROM users;"

# 导出证书数据
sqlite3 database.db ".mode csv" ".output certificates.csv" "SELECT * FROM certificates;"
```

### 2. 导入到 Supabase
使用 Supabase Dashboard 的 "Table Editor" 或 SQL Editor 导入数据。

### 3. 文件迁移
将现有的证书文件上传到 Supabase Storage：
```javascript
// 批量上传脚本示例
const uploadFiles = async () => {
  const files = await fs.readdir('./uploads');
  for (const file of files) {
    const { data, error } = await supabase.storage
      .from('certificates')
      .upload(`migrated/${file}`, fileBuffer);
  }
};
```

## 🧪 第六步：测试验证

### 1. 功能测试清单
- [ ] 用户登录/登出
- [ ] 证书提交
- [ ] 证书审核
- [ ] 学生管理
- [ ] Excel 导入/导出
- [ ] 文件上传/下载
- [ ] 权限控制

### 2. 性能测试
- 页面加载速度
- API 响应时间
- 文件上传速度
- 数据库查询性能

### 3. 安全测试
- 认证绕过测试
- 权限提升测试
- SQL 注入测试
- 文件上传安全测试

## 🔧 第七步：监控和维护

### 1. Vercel 监控
- 查看部署日志
- 监控函数执行时间
- 检查错误率

### 2. Supabase 监控
- 数据库性能监控
- API 使用情况
- 存储使用量

### 3. 日志管理
系统会自动记录操作日志到 `operation_logs` 表，包括：
- 用户登录/登出
- 证书提交/审核
- 数据修改操作
- 系统错误

## 🚨 故障排除

### 常见问题

1. **构建失败**
   - 检查 Node.js 版本
   - 确认所有依赖已安装
   - 检查环境变量配置

2. **API 调用失败**
   - 验证 Supabase 连接
   - 检查 RLS 策略
   - 确认 API 密钥正确

3. **文件上传失败**
   - 检查存储桶配置
   - 验证文件大小限制
   - 确认权限设置

4. **认证问题**
   - 检查 JWT 配置
   - 验证用户权限
   - 确认密码哈希

### 联系支持
如遇到技术问题，请：
1. 检查 Vercel 部署日志
2. 查看 Supabase 错误日志
3. 参考官方文档
4. 提交 GitHub Issue

## 📈 后续优化

### 性能优化
- 启用 CDN 缓存
- 图片压缩优化
- 数据库索引优化
- API 响应缓存

### 功能扩展
- 移动端适配
- 批量操作功能
- 数据分析报表
- 消息通知系统

### 安全加固
- 双因素认证
- API 限流
- 审计日志增强
- 数据备份策略
