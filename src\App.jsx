import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/common/ProtectedRoute';
import Login from './pages/auth/Login';
import StudentLayout from './components/layout/StudentLayout';
import TeacherLayout from './components/layout/TeacherLayout';

// Student Pages
import StudentDashboard from './pages/student/Dashboard';
import StudentCertificates from './pages/student/Certificates';
import StudentScores from './pages/student/Scores';
import StudentAnnouncements from './pages/student/Announcements';

// Teacher Pages
import TeacherDashboard from './pages/teacher/Dashboard';
import CertificateAudit from './pages/teacher/CertificateAudit';
import StudentManagement from './pages/teacher/StudentManagement';
import CourseGrades from './pages/teacher/CourseGrades';
import Reports from './pages/teacher/Reports';
import TeacherAnnouncements from './pages/teacher/Announcements';
import SystemLogs from './pages/teacher/SystemLogs';
import SystemSettings from './pages/teacher/SystemSettings';

// Auth Pages
import ChangePassword from './pages/auth/ChangePassword';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />

            {/* Protected Student Routes */}
            <Route path="/student" element={
              <ProtectedRoute requiredRole="student">
                <StudentLayout />
              </ProtectedRoute>
            }>
              <Route index element={<Navigate to="/student/dashboard" replace />} />
              <Route path="dashboard" element={<StudentDashboard />} />
              <Route path="certificates" element={<StudentCertificates />} />
              <Route path="scores" element={<StudentScores />} />
              <Route path="announcements" element={<StudentAnnouncements />} />
              <Route path="change-password" element={<ChangePassword />} />
            </Route>

            {/* Protected Teacher Routes */}
            <Route path="/teacher" element={
              <ProtectedRoute requiredRole="teacher">
                <TeacherLayout />
              </ProtectedRoute>
            }>
              <Route index element={<Navigate to="/teacher/dashboard" replace />} />
              <Route path="dashboard" element={<TeacherDashboard />} />
              <Route path="audit" element={<CertificateAudit />} />
              <Route path="students" element={<StudentManagement />} />
              <Route path="grades" element={<CourseGrades />} />
              <Route path="reports" element={<Reports />} />
              <Route path="announcements" element={<TeacherAnnouncements />} />
              <Route path="logs" element={<SystemLogs />} />
              <Route path="settings" element={<SystemSettings />} />
              <Route path="change-password" element={<ChangePassword />} />
            </Route>

            {/* Default Redirect */}
            <Route path="/" element={<Navigate to="/login" replace />} />

            {/* 404 Page */}
            <Route path="*" element={
              <div className="min-h-screen flex items-center justify-center bg-gray-50">
                <div className="text-center">
                  <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                  <p className="text-gray-600 mb-8">页面未找到</p>
                  <a href="/login" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    返回登录
                  </a>
                </div>
              </div>
            } />
          </Routes>

          {/* Global Toast Notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
              success: {
                style: {
                  background: '#10b981',
                },
              },
              error: {
                style: {
                  background: '#ef4444',
                },
              },
            }}
          />
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
