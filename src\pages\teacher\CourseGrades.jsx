import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import * as XLSX from 'xlsx';

const CourseGrades = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState([]);
  const [courses, setCourses] = useState([]);
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [showAddCourseModal, setShowAddCourseModal] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [newCourse, setNewCourse] = useState({
    course_name: '',
    course_code: '',
    credits: 1,
    semester: '2024-1'
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Mock data for demonstration
      setStudents([
        {
          id: 1,
          name: '张三',
          student_id: '2021001',
          class_name: '软件工程1班',
          courses: [
            { course_name: '数据结构', course_code: 'CS101', credits: 4, grade: 85, semester: '2024-1' },
            { course_name: '算法设计', course_code: 'CS102', credits: 3, grade: 92, semester: '2024-1' },
            { course_name: '数据库原理', course_code: 'CS201', credits: 3, grade: 78, semester: '2024-1' }
          ]
        },
        {
          id: 2,
          name: '李四',
          student_id: '2021002',
          class_name: '软件工程1班',
          courses: [
            { course_name: '数据结构', course_code: 'CS101', credits: 4, grade: 90, semester: '2024-1' },
            { course_name: '算法设计', course_code: 'CS102', credits: 3, grade: 88, semester: '2024-1' },
            { course_name: '数据库原理', course_code: 'CS201', credits: 3, grade: 85, semester: '2024-1' }
          ]
        }
      ]);

      setCourses([
        { id: 1, course_name: '数据结构', course_code: 'CS101', credits: 4, semester: '2024-1' },
        { id: 2, course_name: '算法设计', course_code: 'CS102', credits: 3, semester: '2024-1' },
        { id: 3, course_name: '数据库原理', course_code: 'CS201', credits: 3, semester: '2024-1' }
      ]);

    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  const calculateGPA = (courses) => {
    if (!courses || courses.length === 0) return 0;
    
    const totalCredits = courses.reduce((sum, course) => sum + course.credits, 0);
    const weightedSum = courses.reduce((sum, course) => sum + (course.grade * course.credits), 0);
    
    return totalCredits > 0 ? (weightedSum / totalCredits).toFixed(2) : 0;
  };

  const handleAddCourse = async () => {
    try {
      // In real implementation, this would call an API
      const course = {
        id: Date.now(),
        ...newCourse,
        credits: parseInt(newCourse.credits)
      };
      
      setCourses([...courses, course]);
      setNewCourse({
        course_name: '',
        course_code: '',
        credits: 1,
        semester: '2024-1'
      });
      setShowAddCourseModal(false);
      toast.success('课程添加成功');
    } catch (error) {
      console.error('Error adding course:', error);
      toast.error('添加课程失败');
    }
  };

  const handleImportGrades = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet);

        // Process imported data
        console.log('Imported data:', jsonData);
        toast.success(`成功导入 ${jsonData.length} 条成绩记录`);
        setShowImportModal(false);
      } catch (error) {
        console.error('Error importing grades:', error);
        toast.error('导入失败，请检查文件格式');
      }
    };
    reader.readAsArrayBuffer(file);
  };

  const exportGrades = () => {
    try {
      const exportData = students.map(student => ({
        学号: student.student_id,
        姓名: student.name,
        班级: student.class_name,
        平均成绩: calculateGPA(student.courses),
        总学分: student.courses.reduce((sum, course) => sum + course.credits, 0),
        课程数量: student.courses.length
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '课程成绩统计');
      XLSX.writeFile(wb, `课程成绩统计_${new Date().toISOString().split('T')[0]}.xlsx`);
      
      toast.success('导出成功');
    } catch (error) {
      console.error('Error exporting grades:', error);
      toast.error('导出失败');
    }
  };

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.student_id.includes(searchTerm) ||
    student.class_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return <LoadingSpinner text="加载课程成绩中..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">课程成绩管理</h1>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddCourseModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            + 添加课程
          </button>
          <button
            onClick={() => setShowImportModal(true)}
            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
          >
            📥 导入成绩
          </button>
          <button
            onClick={exportGrades}
            className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors"
          >
            📤 导出统计
          </button>
        </div>
      </div>

      {/* Search */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="flex items-center space-x-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="搜索学生姓名、学号或班级..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <button
            onClick={fetchData}
            className="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200"
          >
            🔄 刷新
          </button>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <span className="text-2xl">👥</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">学生总数</p>
              <p className="text-2xl font-bold text-gray-900">{students.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <span className="text-2xl">📚</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">课程总数</p>
              <p className="text-2xl font-bold text-gray-900">{courses.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <span className="text-2xl">📊</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">平均GPA</p>
              <p className="text-2xl font-bold text-gray-900">
                {students.length > 0 
                  ? (students.reduce((sum, student) => sum + parseFloat(calculateGPA(student.courses)), 0) / students.length).toFixed(2)
                  : '0.00'
                }
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <span className="text-2xl">🎯</span>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">总成绩记录</p>
              <p className="text-2xl font-bold text-gray-900">
                {students.reduce((sum, student) => sum + student.courses.length, 0)}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Students List */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            学生课程成绩列表
          </h3>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    学生信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    课程数量
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    总学分
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    平均成绩(GPA)
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredStudents.map((student) => (
                  <tr key={student.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{student.name}</div>
                        <div className="text-sm text-gray-500">{student.student_id} - {student.class_name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {student.courses.length}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {student.courses.reduce((sum, course) => sum + course.credits, 0)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="text-sm font-medium text-gray-900">
                        {calculateGPA(student.courses)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => setSelectedStudent(student)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        查看详情
                      </button>
                      <button
                        className="text-green-600 hover:text-green-900"
                      >
                        编辑成绩
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseGrades;
