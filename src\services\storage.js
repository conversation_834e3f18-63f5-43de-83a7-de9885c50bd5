import { supabase } from './supabase';
import { v4 as uuidv4 } from 'uuid';

class StorageService {
  constructor() {
    this.certificatesBucket = 'certificates';
    this.excelBucket = 'excel-files';
  }

  // Upload certificate file
  async uploadCertificate(file, userId) {
    try {
      // Validate file type
      const allowedTypes = [
        'image/jpeg',
        'image/jpg', 
        'image/png',
        'image/gif',
        'image/bmp',
        'image/webp',
        'image/svg+xml',
        'application/pdf'
      ];

      if (!allowedTypes.includes(file.type)) {
        throw new Error('只能上传图片文件 (JPG, PNG, GIF, BMP, WebP, SVG) 或PDF文件');
      }

      // Validate file size (50MB max)
      const maxSize = 50 * 1024 * 1024;
      if (file.size > maxSize) {
        throw new Error('文件大小不能超过50MB');
      }

      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}/${uuidv4()}.${fileExt}`;

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(this.certificatesBucket)
        .upload(fileName, file, {
          contentType: file.type,
          upsert: false
        });

      if (error) {
        console.error('File upload error:', error);
        throw new Error('文件上传失败: ' + error.message);
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(this.certificatesBucket)
        .getPublicUrl(fileName);

      return {
        path: fileName,
        url: publicUrl,
        size: file.size,
        type: file.type,
        name: file.name
      };
    } catch (error) {
      console.error('Upload certificate error:', error);
      throw error;
    }
  }

  // Upload Excel file for import
  async uploadExcelFile(file, userId) {
    try {
      // Validate file type
      const allowedTypes = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ];

      if (!allowedTypes.includes(file.type)) {
        throw new Error('只能上传Excel文件 (.xls, .xlsx)');
      }

      // Validate file size (10MB max for Excel files)
      const maxSize = 10 * 1024 * 1024;
      if (file.size > maxSize) {
        throw new Error('Excel文件大小不能超过10MB');
      }

      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}/${Date.now()}.${fileExt}`;

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from(this.excelBucket)
        .upload(fileName, file, {
          contentType: file.type,
          upsert: false
        });

      if (error) {
        console.error('Excel upload error:', error);
        throw new Error('Excel文件上传失败: ' + error.message);
      }

      return {
        path: fileName,
        size: file.size,
        type: file.type,
        name: file.name
      };
    } catch (error) {
      console.error('Upload Excel error:', error);
      throw error;
    }
  }

  // Delete file from storage
  async deleteFile(bucketName, filePath) {
    try {
      const { error } = await supabase.storage
        .from(bucketName)
        .remove([filePath]);

      if (error) {
        console.error('File deletion error:', error);
        throw new Error('文件删除失败: ' + error.message);
      }

      return true;
    } catch (error) {
      console.error('Delete file error:', error);
      throw error;
    }
  }

  // Delete certificate file
  async deleteCertificate(filePath) {
    return this.deleteFile(this.certificatesBucket, filePath);
  }

  // Delete Excel file
  async deleteExcelFile(filePath) {
    return this.deleteFile(this.excelBucket, filePath);
  }

  // Get file URL
  async getFileUrl(bucketName, filePath) {
    try {
      const { data } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      return data.publicUrl;
    } catch (error) {
      console.error('Get file URL error:', error);
      throw error;
    }
  }

  // Get certificate URL
  async getCertificateUrl(filePath) {
    return this.getFileUrl(this.certificatesBucket, filePath);
  }

  // Download file as blob
  async downloadFile(bucketName, filePath) {
    try {
      const { data, error } = await supabase.storage
        .from(bucketName)
        .download(filePath);

      if (error) {
        console.error('File download error:', error);
        throw new Error('文件下载失败: ' + error.message);
      }

      return data;
    } catch (error) {
      console.error('Download file error:', error);
      throw error;
    }
  }

  // Download certificate file
  async downloadCertificate(filePath) {
    return this.downloadFile(this.certificatesBucket, filePath);
  }

  // List files in a folder
  async listFiles(bucketName, folderPath = '') {
    try {
      const { data, error } = await supabase.storage
        .from(bucketName)
        .list(folderPath);

      if (error) {
        console.error('List files error:', error);
        throw new Error('获取文件列表失败: ' + error.message);
      }

      return data;
    } catch (error) {
      console.error('List files error:', error);
      throw error;
    }
  }

  // List user's certificate files
  async listUserCertificates(userId) {
    return this.listFiles(this.certificatesBucket, userId);
  }

  // Get file info
  async getFileInfo(bucketName, filePath) {
    try {
      const { data, error } = await supabase.storage
        .from(bucketName)
        .list('', {
          search: filePath
        });

      if (error) {
        console.error('Get file info error:', error);
        throw new Error('获取文件信息失败: ' + error.message);
      }

      return data.find(file => file.name === filePath.split('/').pop());
    } catch (error) {
      console.error('Get file info error:', error);
      throw error;
    }
  }

  // Create signed URL for temporary access
  async createSignedUrl(bucketName, filePath, expiresIn = 3600) {
    try {
      const { data, error } = await supabase.storage
        .from(bucketName)
        .createSignedUrl(filePath, expiresIn);

      if (error) {
        console.error('Create signed URL error:', error);
        throw new Error('创建临时链接失败: ' + error.message);
      }

      return data.signedUrl;
    } catch (error) {
      console.error('Create signed URL error:', error);
      throw error;
    }
  }

  // Create signed URL for certificate
  async createCertificateSignedUrl(filePath, expiresIn = 3600) {
    return this.createSignedUrl(this.certificatesBucket, filePath, expiresIn);
  }

  // Batch upload files
  async batchUploadCertificates(files, userId) {
    const results = [];
    const errors = [];

    for (let i = 0; i < files.length; i++) {
      try {
        const result = await this.uploadCertificate(files[i], userId);
        results.push({
          index: i,
          file: files[i],
          result
        });
      } catch (error) {
        errors.push({
          index: i,
          file: files[i],
          error: error.message
        });
      }
    }

    return {
      success: results,
      errors,
      total: files.length,
      successCount: results.length,
      errorCount: errors.length
    };
  }

  // Clean up temporary files (for admin use)
  async cleanupTempFiles(olderThanDays = 7) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      // This would require admin privileges and custom logic
      // Implementation depends on specific cleanup requirements
      console.log('Cleanup temp files older than:', cutoffDate);
      
      // Return cleanup summary
      return {
        cleaned: 0,
        errors: 0,
        message: 'Cleanup completed'
      };
    } catch (error) {
      console.error('Cleanup error:', error);
      throw error;
    }
  }
}

// Create singleton instance
const storageService = new StorageService();

export default storageService;
