import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import apiService from '../../services/api';
import toast from 'react-hot-toast';
import * as XLSX from 'xlsx';

const SystemLogs = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [logs, setLogs] = useState([]);
  const [filteredLogs, setFilteredLogs] = useState([]);
  const [filters, setFilters] = useState({
    dateRange: 'today',
    logType: 'all',
    userId: '',
    action: 'all'
  });
  const [dateRange, setDateRange] = useState({
    startDate: '',
    endDate: ''
  });
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    fetchLogs();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [logs, filters, dateRange]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      
      // Get system logs using API service
      const logs = await apiService.getSystemLogs();
      setLogs(logs);
    } catch (error) {
      console.error('Error fetching logs:', error);
      toast.error('获取系统日志失败');

      // Fallback to mock data
      setLogs([
        {
          id: 1,
          user_id: 'admin',
          user_name: '系统管理员',
          user_role: 'admin',
          action: 'login',
          action_type: 'auth',
          description: '用户登录系统',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          created_at: '2024-07-07T09:00:00Z',
          status: 'success',
          details: { login_method: 'password' }
        },
        {
          id: 2,
          user_id: '2021001',
          user_name: '张三',
          user_role: 'student',
          action: 'submit_certificate',
          action_type: 'certificate',
          description: '提交证书：全国大学生数学建模竞赛',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          created_at: '2024-07-07T10:30:00Z',
          status: 'success',
          details: { certificate_id: 15, category: '学科竞赛' }
        },
        {
          id: 3,
          user_id: 'teacher001',
          user_name: '李老师',
          user_role: 'teacher',
          action: 'audit_certificate',
          action_type: 'audit',
          description: '审核证书：全国大学生数学建模竞赛',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          created_at: '2024-07-07T11:15:00Z',
          status: 'success',
          details: { certificate_id: 15, audit_result: 'approved', score: 15 }
        },
        {
          id: 4,
          user_id: '2021002',
          user_name: '李四',
          user_role: 'student',
          action: 'login_failed',
          action_type: 'auth',
          description: '登录失败：密码错误',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
          created_at: '2024-07-07T12:00:00Z',
          status: 'failed',
          details: { reason: 'invalid_password', attempts: 3 }
        },
        {
          id: 5,
          user_id: 'admin',
          user_name: '系统管理员',
          user_role: 'admin',
          action: 'create_announcement',
          action_type: 'system',
          description: '发布系统公告：证书提交截止时间提醒',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          created_at: '2024-07-07T14:20:00Z',
          status: 'success',
          details: { announcement_id: 1, type: 'important' }
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...logs];

    // Date range filter
    if (filters.dateRange === 'custom' && dateRange.startDate && dateRange.endDate) {
      const start = new Date(dateRange.startDate);
      const end = new Date(dateRange.endDate);
      end.setHours(23, 59, 59, 999);
      
      filtered = filtered.filter(log => {
        const logDate = new Date(log.created_at);
        return logDate >= start && logDate <= end;
      });
    } else if (filters.dateRange !== 'all') {
      const now = new Date();
      let startDate;
      
      switch (filters.dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        default:
          startDate = null;
      }
      
      if (startDate) {
        filtered = filtered.filter(log => new Date(log.created_at) >= startDate);
      }
    }

    // Log type filter
    if (filters.logType !== 'all') {
      filtered = filtered.filter(log => log.action_type === filters.logType);
    }

    // User ID filter
    if (filters.userId) {
      filtered = filtered.filter(log => 
        log.user_id.toLowerCase().includes(filters.userId.toLowerCase()) ||
        log.user_name.toLowerCase().includes(filters.userId.toLowerCase())
      );
    }

    // Action filter
    if (filters.action !== 'all') {
      filtered = filtered.filter(log => log.action === filters.action);
    }

    setFilteredLogs(filtered);
  };

  const handleExportLogs = async () => {
    setExporting(true);
    try {
      const exportData = filteredLogs.map(log => ({
        '时间': new Date(log.created_at).toLocaleString(),
        '用户ID': log.user_id,
        '用户姓名': log.user_name,
        '用户角色': getRoleLabel(log.user_role),
        '操作类型': getActionTypeLabel(log.action_type),
        '操作': getActionLabel(log.action),
        '描述': log.description,
        'IP地址': log.ip_address,
        '状态': log.status === 'success' ? '成功' : '失败',
        '详情': JSON.stringify(log.details || {})
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, '系统日志');
      
      // Set column widths
      const colWidths = [
        { wch: 20 }, // 时间
        { wch: 12 }, // 用户ID
        { wch: 10 }, // 用户姓名
        { wch: 8 },  // 用户角色
        { wch: 10 }, // 操作类型
        { wch: 15 }, // 操作
        { wch: 30 }, // 描述
        { wch: 15 }, // IP地址
        { wch: 6 },  // 状态
        { wch: 20 }  // 详情
      ];
      ws['!cols'] = colWidths;

      const filename = `系统日志_${filters.dateRange}_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, filename);
      toast.success('日志导出成功');
    } catch (error) {
      console.error('Export logs error:', error);
      toast.error('日志导出失败');
    } finally {
      setExporting(false);
    }
  };

  const getActionTypeLabel = (type) => {
    const types = {
      auth: '认证',
      certificate: '证书',
      audit: '审核',
      system: '系统',
      user: '用户管理',
      data: '数据操作'
    };
    return types[type] || type;
  };

  const getActionLabel = (action) => {
    const actions = {
      login: '登录',
      logout: '登出',
      login_failed: '登录失败',
      submit_certificate: '提交证书',
      update_certificate: '更新证书',
      delete_certificate: '删除证书',
      audit_certificate: '审核证书',
      create_announcement: '发布公告',
      update_announcement: '更新公告',
      delete_announcement: '删除公告',
      create_user: '创建用户',
      update_user: '更新用户',
      delete_user: '删除用户',
      export_data: '导出数据',
      import_data: '导入数据'
    };
    return actions[action] || action;
  };

  const getRoleLabel = (role) => {
    const roles = {
      admin: '管理员',
      teacher: '教师',
      student: '学生'
    };
    return roles[role] || role;
  };

  const getStatusBadge = (status) => {
    const badges = {
      success: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      warning: 'bg-yellow-100 text-yellow-800'
    };
    const labels = {
      success: '成功',
      failed: '失败',
      warning: '警告'
    };
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${badges[status] || badges.success}`}>
        {labels[status] || status}
      </span>
    );
  };

  const getActionTypeBadge = (type) => {
    const badges = {
      auth: 'bg-blue-100 text-blue-800',
      certificate: 'bg-purple-100 text-purple-800',
      audit: 'bg-orange-100 text-orange-800',
      system: 'bg-gray-100 text-gray-800',
      user: 'bg-indigo-100 text-indigo-800',
      data: 'bg-pink-100 text-pink-800'
    };
    return (
      <span className={`px-2 py-1 text-xs font-medium rounded-full ${badges[type] || badges.system}`}>
        {getActionTypeLabel(type)}
      </span>
    );
  };

  if (loading) {
    return <LoadingSpinner text="加载系统日志中..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">系统日志</h1>
        <button
          onClick={handleExportLogs}
          disabled={exporting}
          className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
        >
          {exporting ? '导出中...' : '📊 导出日志'}
        </button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📝</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">总日志数</dt>
                  <dd className="text-lg font-medium text-gray-900">{logs.length}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">✅</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">成功操作</dt>
                  <dd className="text-lg font-medium text-green-600">
                    {logs.filter(log => log.status === 'success').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">❌</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">失败操作</dt>
                  <dd className="text-lg font-medium text-red-600">
                    {logs.filter(log => log.status === 'failed').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">🔍</span>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">筛选结果</dt>
                  <dd className="text-lg font-medium text-blue-600">{filteredLogs.length}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">筛选条件</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">时间范围</label>
            <select
              value={filters.dateRange}
              onChange={(e) => setFilters({...filters, dateRange: e.target.value})}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">全部时间</option>
              <option value="today">今天</option>
              <option value="week">最近一周</option>
              <option value="month">本月</option>
              <option value="custom">自定义</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">操作类型</label>
            <select
              value={filters.logType}
              onChange={(e) => setFilters({...filters, logType: e.target.value})}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">全部类型</option>
              <option value="auth">认证</option>
              <option value="certificate">证书</option>
              <option value="audit">审核</option>
              <option value="system">系统</option>
              <option value="user">用户管理</option>
              <option value="data">数据操作</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">用户搜索</label>
            <input
              type="text"
              value={filters.userId}
              onChange={(e) => setFilters({...filters, userId: e.target.value})}
              placeholder="用户ID或姓名"
              className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">具体操作</label>
            <select
              value={filters.action}
              onChange={(e) => setFilters({...filters, action: e.target.value})}
              className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">全部操作</option>
              <option value="login">登录</option>
              <option value="logout">登出</option>
              <option value="submit_certificate">提交证书</option>
              <option value="audit_certificate">审核证书</option>
              <option value="create_announcement">发布公告</option>
              <option value="export_data">导出数据</option>
            </select>
          </div>
        </div>

        {/* Custom Date Range */}
        {filters.dateRange === 'custom' && (
          <div className="mt-4 grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">开始日期</label>
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">结束日期</label>
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
                className="block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        )}
      </div>

      {/* Logs Table */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            操作日志 ({filteredLogs.length} 条记录)
          </h3>

          {filteredLogs.length === 0 ? (
            <div className="text-center py-8">
              <span className="text-4xl mb-4 block">📝</span>
              <p className="text-gray-500">暂无符合条件的日志记录</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      时间
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      用户
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作类型
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      操作描述
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IP地址
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      状态
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(log.created_at).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{log.user_name}</div>
                        <div className="text-sm text-gray-500">{log.user_id} ({getRoleLabel(log.user_role)})</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getActionTypeBadge(log.action_type)}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                        {log.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {log.ip_address}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(log.status)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SystemLogs;
