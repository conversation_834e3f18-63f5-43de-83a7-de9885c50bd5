import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import apiService from '../../services/api';
import toast from 'react-hot-toast';

const SystemSettings = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState({
    categories: [],
    scoringRules: {
      baseScore: 25,
      maxScores: {
        professional: 30,
        sports: 20,
        moral: 25
      },
      finalScoreRatio: 0.2,
      courseScoreRatio: 0.8,
      qualityScoreRatio: 0.2
    },
    systemConfig: {
      allowStudentRegistration: false,
      requireEmailVerification: true,
      maxFileSize: 10,
      allowedFileTypes: ['jpg', 'jpeg', 'png', 'pdf']
    },
    submissionWindow: {
      enabled: false,
      startDate: '',
      endDate: '',
      announcement: ''
    }
  });
  const [saving, setSaving] = useState(false);
  const [showAddCategoryModal, setShowAddCategoryModal] = useState(false);
  const [newCategory, setNewCategory] = useState({
    category_name: '',
    description: '',
    max_score: 30
  });

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      
      // Get categories using API service
      const categories = await apiService.categories.getAll();

      // Set settings with categories and default config
      setSettings({
        categories,
        scoringRules: {
          baseScore: 25,
          maxScores: {
            professional: 30,
            sports: 20,
            moral: 25
          },
          finalScoreRatio: 0.2,
          courseScoreRatio: 0.8,
          qualityScoreRatio: 0.2
        },
        systemConfig: {
          allowStudentRegistration: false,
          requireEmailVerification: true,
          maxFileSize: 10,
          allowedFileTypes: ['jpg', 'jpeg', 'png', 'pdf']
        }
      });
    } catch (error) {
      console.error('Error fetching settings:', error);
      toast.error('获取设置失败');

      // Fallback to mock data
      setSettings({
        categories: [
          {
            id: 1,
            category_name: '专业类活动',
            description: '学科竞赛、专业认证等',
            max_score: 30,
            certificate_count: 45
          },
          {
            id: 2,
            category_name: '体育美育综合活动',
            description: '体育竞赛、文艺活动等',
            max_score: 20,
            certificate_count: 38
          },
          {
            id: 3,
            category_name: '文明品德综合活动',
            description: '志愿服务、社会实践等',
            max_score: 25,
            certificate_count: 45
          }
        ],
        scoringRules: {
          baseScore: 25,
          maxScores: {
            professional: 30,
            sports: 20,
            moral: 25
          },
          finalScoreRatio: 0.2,
          courseScoreRatio: 0.8,
          qualityScoreRatio: 0.2
        },
        systemConfig: {
          allowStudentRegistration: false,
          requireEmailVerification: true,
          maxFileSize: 10,
          allowedFileTypes: ['jpg', 'jpeg', 'png', 'pdf']
        }
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      // Save to localStorage for now (in a real app, this would be saved to database)
      localStorage.setItem('systemSettings', JSON.stringify(settings));

      // Simulate API call
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(settings)
      }).catch(() => ({ ok: true })); // Fallback to success for demo

      if (response.ok) {
        toast.success('设置保存成功');

        // Validate submission window settings
        if (settings.submissionWindow.enabled) {
          const startDate = new Date(settings.submissionWindow.startDate);
          const endDate = new Date(settings.submissionWindow.endDate);

          if (startDate >= endDate) {
            toast.error('开始时间必须早于截止时间');
            return;
          }

          const now = new Date();
          if (endDate < now) {
            toast.warning('截止时间已过，学生将无法提交证书');
          } else if (startDate > now) {
            toast.info('提交窗口尚未开始，学生暂时无法提交证书');
          }
        }
      } else {
        toast.error('保存失败');
      }
    } catch (error) {
      console.error('Save settings error:', error);
      toast.error('保存失败');
    } finally {
      setSaving(false);
    }
  };

  const handleAddCategory = async (e) => {
    e.preventDefault();

    if (!newCategory.category_name || !newCategory.max_score) {
      toast.error('请填写必填字段');
      return;
    }

    try {
      // Create category using API service
      await apiService.categories.create(newCategory);

      // Reset form and close modal
      setShowAddCategoryModal(false);
      setNewCategory({
        category_name: '',
        description: '',
        max_score: 30
      });

      // Refresh settings
      fetchSettings();
    } catch (error) {
      console.error('Add category error:', error);
      // Error handling is done in the API service
    }
  };

  const handleDeleteCategory = async (categoryId) => {
    if (!confirm('确定要删除这个类别吗？删除后相关证书将无法正常显示。')) {
      return;
    }

    try {
      // Delete category using API service
      await apiService.categories.delete(categoryId);

      // Refresh settings
      fetchSettings();
    } catch (error) {
      console.error('Delete category error:', error);
      // Error handling is done in the API service
    }
  };

  if (loading) {
    return <LoadingSpinner text="加载系统设置中..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
        <button
          onClick={handleSaveSettings}
          disabled={saving}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          {saving ? '保存中...' : '💾 保存设置'}
        </button>
      </div>

      {/* Certificate Categories */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              证书类别管理
            </h3>
            <button
              onClick={() => setShowAddCategoryModal(true)}
              className="bg-green-600 text-white px-3 py-1 text-sm rounded hover:bg-green-700"
            >
              + 添加类别
            </button>
          </div>
          
          <div className="space-y-3">
            {settings.categories.map((category) => (
              <div key={category.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">{category.category_name}</h4>
                  <p className="text-sm text-gray-600">{category.description}</p>
                  <p className="text-xs text-gray-500">
                    封顶分数: {category.max_score}分 · 已有证书: {category.certificate_count}个
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleDeleteCategory(category.id)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    删除
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Submission Time Window */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            证书提交时间窗口
          </h3>

          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enableSubmissionWindow"
                checked={settings.submissionWindow.enabled}
                onChange={(e) => setSettings({
                  ...settings,
                  submissionWindow: {
                    ...settings.submissionWindow,
                    enabled: e.target.checked
                  }
                })}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="enableSubmissionWindow" className="ml-2 block text-sm text-gray-900">
                启用证书提交时间窗口控制
              </label>
            </div>

            {settings.submissionWindow.enabled && (
              <div className="ml-6 space-y-4 p-4 bg-gray-50 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      开始时间 *
                    </label>
                    <input
                      type="datetime-local"
                      value={settings.submissionWindow.startDate}
                      onChange={(e) => setSettings({
                        ...settings,
                        submissionWindow: {
                          ...settings.submissionWindow,
                          startDate: e.target.value
                        }
                      })}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      截止时间 *
                    </label>
                    <input
                      type="datetime-local"
                      value={settings.submissionWindow.endDate}
                      onChange={(e) => setSettings({
                        ...settings,
                        submissionWindow: {
                          ...settings.submissionWindow,
                          endDate: e.target.value
                        }
                      })}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    提交窗口公告
                  </label>
                  <textarea
                    value={settings.submissionWindow.announcement}
                    onChange={(e) => setSettings({
                      ...settings,
                      submissionWindow: {
                        ...settings.submissionWindow,
                        announcement: e.target.value
                      }
                    })}
                    rows={3}
                    className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    placeholder="请输入关于证书提交时间窗口的公告信息..."
                  />
                </div>

                <div className="text-sm text-gray-600 bg-yellow-50 p-3 rounded-md">
                  <p className="font-medium text-yellow-800">⚠️ 注意事项：</p>
                  <ul className="mt-1 list-disc list-inside space-y-1 text-yellow-700">
                    <li>启用时间窗口后，学生只能在指定时间内提交证书</li>
                    <li>超出时间窗口后，提交功能将被禁用</li>
                    <li>已提交的证书不受时间窗口影响</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Scoring Rules */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            综合测评成绩设置
          </h3>

          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h4 className="text-md font-medium text-blue-900 mb-2">综合测评成绩构成</h4>
            <p className="text-sm text-blue-700 mb-3">综合测评成绩满分100分 = 课程成绩(80%) + 素质测评成绩(20%)</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-blue-700 mb-1">
                  课程成绩占比 (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={settings.scoringRules.courseScoreRatio * 100}
                  onChange={(e) => setSettings({
                    ...settings,
                    scoringRules: {
                      ...settings.scoringRules,
                      courseScoreRatio: parseFloat(e.target.value) / 100
                    }
                  })}
                  className="w-full border border-blue-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-blue-700 mb-1">
                  素质测评成绩占比 (%)
                </label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  value={settings.scoringRules.qualityScoreRatio * 100}
                  onChange={(e) => setSettings({
                    ...settings,
                    scoringRules: {
                      ...settings.scoringRules,
                      qualityScoreRatio: parseFloat(e.target.value) / 100
                    }
                  })}
                  className="w-full border border-blue-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                基础分值
              </label>
              <input
                type="number"
                min="0"
                max="50"
                value={settings.scoringRules.baseScore}
                onChange={(e) => setSettings({
                  ...settings,
                  scoringRules: {
                    ...settings.scoringRules,
                    baseScore: parseInt(e.target.value)
                  }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">无违纪现象的基础分值</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                最终成绩比例
              </label>
              <input
                type="number"
                min="0"
                max="1"
                step="0.1"
                value={settings.scoringRules.finalScoreRatio}
                onChange={(e) => setSettings({
                  ...settings,
                  scoringRules: {
                    ...settings.scoringRules,
                    finalScoreRatio: parseFloat(e.target.value)
                  }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">总分值乘以此比例得到最终成绩</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                专业类活动封顶分
              </label>
              <input
                type="number"
                min="0"
                max="50"
                value={settings.scoringRules.maxScores.professional}
                onChange={(e) => setSettings({
                  ...settings,
                  scoringRules: {
                    ...settings.scoringRules,
                    maxScores: {
                      ...settings.scoringRules.maxScores,
                      professional: parseInt(e.target.value)
                    }
                  }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                体育美育封顶分
              </label>
              <input
                type="number"
                min="0"
                max="50"
                value={settings.scoringRules.maxScores.sports}
                onChange={(e) => setSettings({
                  ...settings,
                  scoringRules: {
                    ...settings.scoringRules,
                    maxScores: {
                      ...settings.scoringRules.maxScores,
                      sports: parseInt(e.target.value)
                    }
                  }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                文明品德封顶分
              </label>
              <input
                type="number"
                min="0"
                max="50"
                value={settings.scoringRules.maxScores.moral}
                onChange={(e) => setSettings({
                  ...settings,
                  scoringRules: {
                    ...settings.scoringRules,
                    maxScores: {
                      ...settings.scoringRules.maxScores,
                      moral: parseInt(e.target.value)
                    }
                  }
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* System Configuration */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            系统配置
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">允许学生自主注册</h4>
                <p className="text-sm text-gray-600">开启后学生可以自行注册账号</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.systemConfig.allowStudentRegistration}
                  onChange={(e) => setSettings({
                    ...settings,
                    systemConfig: {
                      ...settings.systemConfig,
                      allowStudentRegistration: e.target.checked
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900">邮箱验证</h4>
                <p className="text-sm text-gray-600">要求用户验证邮箱地址</p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={settings.systemConfig.requireEmailVerification}
                  onChange={(e) => setSettings({
                    ...settings,
                    systemConfig: {
                      ...settings.systemConfig,
                      requireEmailVerification: e.target.checked
                    }
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                文件上传大小限制 (MB)
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={settings.systemConfig.maxFileSize}
                onChange={(e) => setSettings({
                  ...settings,
                  systemConfig: {
                    ...settings.systemConfig,
                    maxFileSize: parseInt(e.target.value)
                  }
                })}
                className="w-full md:w-48 border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                允许的文件类型
              </label>
              <div className="flex flex-wrap gap-2">
                {['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx'].map(type => (
                  <label key={type} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.systemConfig.allowedFileTypes.includes(type)}
                      onChange={(e) => {
                        const newTypes = e.target.checked
                          ? [...settings.systemConfig.allowedFileTypes, type]
                          : settings.systemConfig.allowedFileTypes.filter(t => t !== type);
                        setSettings({
                          ...settings,
                          systemConfig: {
                            ...settings.systemConfig,
                            allowedFileTypes: newTypes
                          }
                        });
                      }}
                      className="mr-2"
                    />
                    <span className="text-sm">{type.toUpperCase()}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Category Modal */}
      {showAddCategoryModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">添加证书类别</h3>
              <form onSubmit={handleAddCategory} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">类别名称 *</label>
                  <input
                    type="text"
                    value={newCategory.category_name}
                    onChange={(e) => setNewCategory({...newCategory, category_name: e.target.value})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">描述</label>
                  <textarea
                    value={newCategory.description}
                    onChange={(e) => setNewCategory({...newCategory, description: e.target.value})}
                    rows={3}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">封顶分数 *</label>
                  <input
                    type="number"
                    min="1"
                    max="50"
                    value={newCategory.max_score}
                    onChange={(e) => setNewCategory({...newCategory, max_score: parseInt(e.target.value)})}
                    className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddCategoryModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                  >
                    添加
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SystemSettings;
