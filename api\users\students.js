import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Helper function to verify user authentication and permissions
async function verifyAuth(req, requiredRole = null, requiredPermission = null) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);
  
  // Verify token with Supabase
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    throw new Error('令牌无效或已过期');
  }

  // Get user profile from database
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();

  if (userError || !userData) {
    throw new Error('用户不存在');
  }

  // Check role requirement
  if (requiredRole && userData.role !== requiredRole) {
    throw new Error('权限不足');
  }

  // Check permission requirement
  if (requiredPermission && !userData.is_super_admin) {
    if (!userData.permissions || !userData.permissions.includes(requiredPermission)) {
      throw new Error('权限不足');
    }
  }

  return userData;
}

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    // Verify authentication - require teacher role
    const currentUser = await verifyAuth(req, 'teacher', 'student_management');

    switch (req.method) {
      case 'GET':
        return await handleGetStudents(req, res, currentUser);
      case 'POST':
        return await handleCreateStudent(req, res, currentUser);
      default:
        return res.status(405).json({ message: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Students API error:', error);
    return res.status(403).json({ message: error.message || '操作失败' });
  }
}

async function handleGetStudents(req, res, currentUser) {
  try {
    const { page = 1, limit = 10, search, class_name } = req.query;
    const offset = (page - 1) * limit;

    let query = supabase
      .from('users')
      .select('id, student_id, name, class_name, created_at, last_login', { count: 'exact' })
      .eq('role', 'student')
      .order('created_at', { ascending: false });

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,student_id.ilike.%${search}%`);
    }

    if (class_name) {
      query = query.eq('class_name', class_name);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: students, error, count } = await query;

    if (error) throw error;

    // Get class list for filters
    const { data: classes } = await supabase
      .from('users')
      .select('class_name')
      .eq('role', 'student')
      .not('class_name', 'is', null);

    const uniqueClasses = [...new Set(classes?.map(c => c.class_name) || [])];

    res.status(200).json({
      data: {
        students,
        pagination: {
          current: parseInt(page),
          pageSize: parseInt(limit),
          total: count,
          totalPages: Math.ceil(count / limit)
        },
        classes: uniqueClasses
      }
    });
  } catch (error) {
    console.error('Get students error:', error);
    res.status(500).json({ message: '获取学生列表失败' });
  }
}

async function handleCreateStudent(req, res, currentUser) {
  try {
    const { student_id, name, class_name, password } = req.body;

    // Validation
    if (!student_id || !name || !class_name) {
      return res.status(400).json({ message: '学号、姓名、班级为必填项' });
    }

    // Check if student_id already exists
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('id')
      .eq('student_id', student_id)
      .single();

    if (existingUser) {
      return res.status(400).json({ message: '学号已存在' });
    }

    // Hash password
    const defaultPassword = password || student_id;
    const passwordHash = await bcrypt.hash(defaultPassword, 10);

    // Create new student
    const { data: newStudent, error: insertError } = await supabase
      .from('users')
      .insert({
        student_id,
        name,
        class_name,
        password_hash: passwordHash,
        role: 'student'
      })
      .select('id, student_id, name, class_name, created_at')
      .single();

    if (insertError) throw insertError;

    // Log the operation
    await supabase
      .from('operation_logs')
      .insert({
        user_id: currentUser.id,
        action: '添加学生',
        details: {
          student_id: newStudent.student_id,
          student_name: newStudent.name,
          class_name: newStudent.class_name
        },
        target_type: 'student',
        target_id: newStudent.id,
        ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        user_agent: req.headers['user-agent']
      });

    res.status(201).json({
      message: '学生添加成功',
      data: { student: newStudent }
    });
  } catch (error) {
    console.error('Create student error:', error);
    res.status(500).json({ message: '添加学生失败' });
  }
}
