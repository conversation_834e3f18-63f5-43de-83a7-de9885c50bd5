import React, { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../../components/common/LoadingSpinner';
import reportsService from '../../services/reports';
import toast from 'react-hot-toast';

const Dashboard = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCertificates: 0,
    pendingCertificates: 0,
    approvedCertificates: 0,
    totalScore: 0,
    categoryScores: {
      professional: 0,
      sports: 0,
      moral: 0
    }
  });

  useEffect(() => {
    if (user) {
      fetchDashboardData();
    }
  }, [user]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Get real dashboard statistics
      const dashboardStats = await reportsService.getDashboardStats(user.id, 'student');
      setStats(dashboardStats);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('获取数据失败');

      // Fallback to mock data if API fails
      const mockStats = {
        totalCertificates: 8,
        pendingCertificates: 2,
        approvedCertificates: 5,
        rejectedCertificates: 1,
        totalScore: 78.5,
        scoreBreakdown: {
          professional: 25.0,
          sports: 18.5,
          moral: 22.0,
          base: 25.0
        }
      };
      setStats(mockStats);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner text="加载个人信息中..." />;
  }

  const StatCard = ({ title, value, icon, color = 'blue' }) => (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <span className="text-2xl">{icon}</span>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                {title}
              </dt>
              <dd className={`text-lg font-medium text-${color}-600`}>
                {value}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-2xl">👨‍🎓</span>
            </div>
          </div>
          <div className="ml-6">
            <h1 className="text-2xl font-bold text-gray-900">
              欢迎回来，{user?.name || user?.student_id}！
            </h1>
            <p className="text-gray-600">
              学号：{user?.student_id} | 班级：{user?.class_name || '未设置'}
            </p>
            <p className="text-sm text-gray-500 mt-1">
              上次登录：{new Date().toLocaleString()}
            </p>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="证书总数"
          value={stats.totalCertificates}
          icon="📜"
          color="blue"
        />
        <StatCard
          title="待审核"
          value={stats.pendingCertificates}
          icon="⏳"
          color="yellow"
        />
        <StatCard
          title="已通过"
          value={stats.approvedCertificates}
          icon="✅"
          color="green"
        />
        <StatCard
          title="总分"
          value={`${stats.totalScore}分`}
          icon="🏆"
          color="purple"
        />
      </div>

      {/* Score Breakdown */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            成绩构成
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-lg mr-2">📚</span>
                <span className="text-sm font-medium text-gray-700">专业类活动</span>
              </div>
              <div className="flex items-center">
                <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${((stats.scoreBreakdown?.professional || 0) / 30) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {stats.scoreBreakdown?.professional || 0}/30分
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-lg mr-2">🏃‍♂️</span>
                <span className="text-sm font-medium text-gray-700">体育美育</span>
              </div>
              <div className="flex items-center">
                <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${((stats.scoreBreakdown?.sports || 0) / 20) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {stats.scoreBreakdown?.sports || 0}/20分
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-lg mr-2">🌟</span>
                <span className="text-sm font-medium text-gray-700">文明品德</span>
              </div>
              <div className="flex items-center">
                <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div
                    className="bg-purple-600 h-2 rounded-full"
                    style={{ width: `${((stats.scoreBreakdown?.moral || 0) / 25) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {stats.scoreBreakdown?.moral || 0}/25分
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <span className="text-lg mr-2">⭐</span>
                <span className="text-sm font-medium text-gray-700">基础分</span>
              </div>
              <div className="flex items-center">
                <div className="w-32 bg-gray-200 rounded-full h-2 mr-3">
                  <div
                    className="bg-yellow-600 h-2 rounded-full"
                    style={{ width: `${((stats.scoreBreakdown?.base || 25) / 25) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {stats.scoreBreakdown?.base || 25}/25分
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            快速操作
          </h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <a
              href="/student/certificates"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 ring-4 ring-white">
                  <span className="text-xl">📜</span>
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true"></span>
                  提交证书
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  上传新的证书文件
                </p>
              </div>
            </a>

            <a
              href="/student/scores"
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-green-50 text-green-700 ring-4 ring-white">
                  <span className="text-xl">📊</span>
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  <span className="absolute inset-0" aria-hidden="true"></span>
                  查看成绩
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  查看详细成绩单
                </p>
              </div>
            </a>

            <button
              onClick={fetchDashboardData}
              className="relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors"
            >
              <div>
                <span className="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 ring-4 ring-white">
                  <span className="text-xl">🔄</span>
                </span>
              </div>
              <div className="mt-4">
                <h3 className="text-lg font-medium">
                  刷新数据
                </h3>
                <p className="mt-2 text-sm text-gray-500">
                  更新最新信息
                </p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
