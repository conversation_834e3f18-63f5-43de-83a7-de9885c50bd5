import { createClient } from '@supabase/supabase-js';
import formidable from 'formidable';
import { v4 as uuidv4 } from 'uuid';

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Disable body parsing for file uploads
export const config = {
  api: {
    bodyParser: false,
  },
};

// Helper function to verify user authentication
async function verifyAuth(req) {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('未提供认证令牌');
  }

  const token = authHeader.substring(7);
  
  // Verify token with Supabase
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    throw new Error('令牌无效或已过期');
  }

  // Get user profile from database
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single();

  if (userError || !userData) {
    throw new Error('用户不存在');
  }

  return userData;
}

// Calculate score based on category and level
function calculateScore(category, level, teamSize = 1, role = 'individual') {
  const levels = category.levels;
  let baseScore = levels[level] || 0;

  // Apply team size adjustment for certain categories
  if (category.requires_team_info && teamSize > 1) {
    if (teamSize <= 3) {
      baseScore = Math.round(baseScore * 1.0); // No reduction for small teams
    } else if (teamSize <= 6) {
      baseScore = Math.round(baseScore * 0.8); // 20% reduction for medium teams
    } else {
      baseScore = Math.round(baseScore * 0.6); // 40% reduction for large teams
    }
  }

  // Apply role adjustment for innovation projects
  if (category.requires_role_info && role === 'member') {
    baseScore = Math.round(baseScore * 0.7); // 30% reduction for members
  }

  return baseScore;
}

export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const user = await verifyAuth(req);

    // Parse form data
    const form = formidable({
      maxFileSize: 50 * 1024 * 1024, // 50MB
      allowEmptyFiles: false,
    });

    const [fields, files] = await form.parse(req);

    // Extract form data
    const certificate_name = Array.isArray(fields.certificate_name) ? fields.certificate_name[0] : fields.certificate_name;
    const category_id = Array.isArray(fields.category_id) ? fields.category_id[0] : fields.category_id;
    const level = Array.isArray(fields.level) ? fields.level[0] : fields.level;
    const award_date = Array.isArray(fields.award_date) ? fields.award_date[0] : fields.award_date;
    const team_size = Array.isArray(fields.team_size) ? parseInt(fields.team_size[0]) : parseInt(fields.team_size) || 1;
    const role = Array.isArray(fields.role) ? fields.role[0] : fields.role || 'individual';
    const audit_activity_id = Array.isArray(fields.audit_activity_id) ? fields.audit_activity_id[0] : fields.audit_activity_id;

    // Validation
    if (!certificate_name || !category_id || !level || !audit_activity_id) {
      return res.status(400).json({ message: '请填写所有必填字段' });
    }

    // Get certificate category
    const { data: category, error: categoryError } = await supabase
      .from('certificate_categories')
      .select('*')
      .eq('id', category_id)
      .single();

    if (categoryError || !category) {
      return res.status(400).json({ message: '证书类别不存在' });
    }

    // Check submission limits
    if (category.submission_limit) {
      const { data: existingCerts, error: countError } = await supabase
        .from('certificates')
        .select('id')
        .eq('user_id', user.id)
        .eq('category_id', category_id)
        .eq('audit_activity_id', audit_activity_id);

      if (countError) {
        throw countError;
      }

      if (existingCerts.length >= category.submission_limit) {
        return res.status(400).json({ 
          message: `该类别最多只能提交${category.submission_limit}个证书` 
        });
      }
    }

    // Calculate score
    const calculatedScore = calculateScore(category, level, team_size, role);

    // Handle file upload to Supabase Storage
    let fileUrl = null;
    let filePath = null;

    if (files.file && files.file[0]) {
      const file = files.file[0];
      const fileExt = file.originalFilename.split('.').pop();
      const fileName = `${user.id}/${uuidv4()}.${fileExt}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('certificates')
        .upload(fileName, file, {
          contentType: file.mimetype,
          upsert: false
        });

      if (uploadError) {
        console.error('File upload error:', uploadError);
        return res.status(500).json({ message: '文件上传失败' });
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('certificates')
        .getPublicUrl(fileName);

      fileUrl = publicUrl;
      filePath = fileName;
    }

    // Insert certificate record
    const { data: newCertificate, error: insertError } = await supabase
      .from('certificates')
      .insert({
        user_id: user.id,
        category_id,
        audit_activity_id,
        certificate_name,
        level,
        score: calculatedScore,
        award_date: award_date || new Date().toISOString().split('T')[0],
        team_size,
        role,
        file_path: filePath,
        file_url: fileUrl,
        status: 'pending'
      })
      .select()
      .single();

    if (insertError) {
      // If certificate insert fails, clean up uploaded file
      if (filePath) {
        await supabase.storage
          .from('certificates')
          .remove([filePath]);
      }
      throw insertError;
    }

    // Log the submission
    await supabase
      .from('operation_logs')
      .insert({
        user_id: user.id,
        action: '提交证书',
        details: {
          certificate_name,
          category_name: category.category_name,
          level,
          score: calculatedScore
        },
        target_type: 'certificate',
        target_id: newCertificate.id,
        ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        user_agent: req.headers['user-agent']
      });

    res.status(201).json({
      message: '证书提交成功',
      data: {
        certificate: newCertificate
      }
    });

  } catch (error) {
    console.error('Certificate submission error:', error);
    return res.status(500).json({ 
      message: error.message || '证书提交失败' 
    });
  }
}
